export interface IDecodedAccessToken {
	exp: number;
	iat: number;
	auth_time: number;
	jti: string;
	iss: string;
	aud: string[];
	sub: string;
	typ: string;
	azp: string;
	sid: string;
	acr: string;
	"allowed-origins": string[];
	realm_access: {
		roles: string[];
	};
	resource_access: {
		[key: string]: {
			roles: string[];
		};
	};
	authorization: {
		permissions: Array<{
			scopes?: string[];
			rsid: string;
			rsname: string;
		}>;
	};
	scope: string;
	email_verified: boolean;
	name: string;
	preferred_username: string;
	given_name: string;
	family_name: string;
	email: string;
}
