"use client";

import { <PERSON>, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { DataTableProps, useOrderColumns } from "@/shared/hooks/table-production/order-colums.hook";
import { DndContext, KeyboardSensor, MouseSensor, TouchSensor, closestCenter, useSensor, useSensors, type DragEndEvent } from "@dnd-kit/core";
import { restrictToFirstScrollableAncestor, restrictToHorizontalAxis } from "@dnd-kit/modifiers";
import { SortableContext, arrayMove, horizontalListSortingStrategy, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Cell, Header, flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { ListFilterIcon, RotateCcw } from "lucide-react";
import React, { CSSProperties } from "react";

const DraggableTableHeader = <TData,>({ header, customIcon }: { header: Header<TData, unknown>; customIcon?: React.ReactNode }) => {
	const { attributes, isDragging, listeners, setNodeRef, transform } = useSortable({
		id: header.column.id,
	});

	const style: CSSProperties = {
		opacity: isDragging ? 0.8 : 1,
		position: "relative",
		transform: CSS.Translate.toString(transform),
		transition: "width transform 0.2s ease-in-out",
		whiteSpace: "nowrap",
		width: header.column.getSize(),
		zIndex: isDragging ? 1 : 0,
	};

	return (
		<TableHead
			ref={setNodeRef}
			className="text-white hover:bg-[#004475] font-semibold cursor-grab active:cursor-grabbing text-center  border-white/20 "
			style={style}
			{...attributes}
			{...listeners}
			colSpan={header.colSpan}
		>
			<div className="flex items-center justify-center">
				{header.isPlaceholder ? null : (
					<>
						{flexRender(header.column.columnDef.header, header.getContext())}
						{customIcon}
					</>
				)}
			</div>
		</TableHead>
	);
};

const DragAlongCell = <TData,>({ cell }: { cell: Cell<TData, unknown> }) => {
	const { isDragging, setNodeRef, transform } = useSortable({
		id: cell.column.id,
	});

	const style: CSSProperties = {
		opacity: isDragging ? 0.8 : 1,
		position: "relative",
		transform: CSS.Translate.toString(transform),
		transition: "width transform 0.2s ease-in-out",
		width: cell.column.getSize(),
		zIndex: isDragging ? 1 : 0,
	};

	return (
		<TableCell style={style} ref={setNodeRef} className="py-2">
			{flexRender(cell.column.columnDef.cell, cell.getContext())}
		</TableCell>
	);
};

export function DataTable<TData, TValue>({ columns, data, tableId = "default-table" }: DataTableProps<TData, TValue>) {
	const { columnOrder, setColumnOrder, resetColumnOrder } = useOrderColumns({ tableId, columns, data });

	const table = useReactTable({
		data,
		columns,
		getCoreRowModel: getCoreRowModel(),
		state: {
			columnOrder,
		},
		onColumnOrderChange: setColumnOrder,
	});

	function handleDragEnd(event: DragEndEvent) {
		const { active, over } = event;
		if (active && over && active.id !== over.id) {
			setColumnOrder(columnOrder => {
				const oldIndex = columnOrder.indexOf(active.id as string);
				const newIndex = columnOrder.indexOf(over.id as string);
				return arrayMove(columnOrder, oldIndex, newIndex);
			});
		}
	}

	const sensors = useSensors(useSensor(MouseSensor, {}), useSensor(TouchSensor, {}), useSensor(KeyboardSensor, {}));

	return (
		<>
			<div className="flex items-center justify-between mb-4">
				<button
					onClick={resetColumnOrder}
					className="flex items-center gap-2 px-3 py-1.5 text-sm bg-gray-100 hover:bg-gray-200 rounded-md border transition-colors"
					title="Resetar ordem das colunas"
				>
					<RotateCcw className="h-4 w-4" />
					Resetar Colunas
				</button>
			</div>
			<DndContext
				collisionDetection={closestCenter}
				modifiers={[restrictToHorizontalAxis, restrictToFirstScrollableAncestor]}
				onDragEnd={handleDragEnd}
				sensors={sensors}
			>
				<div className="rounded-[10px] border mt-6 overflow-hidden relative">
					<Table>
						<TableHeader className="bg-[#004475] hover:bg-[#004475]/80">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id} className="bg-[#004475] hover:bg-[#004475]/80 border-0">
									<SortableContext items={columnOrder} strategy={horizontalListSortingStrategy}>
										{headerGroup.headers.map(header => (
											<DraggableTableHeader
												key={header.id}
												header={header}
												customIcon={<ListFilterIcon className="ml-2 h-4 w-4 text-white" />}
											/>
										))}
									</SortableContext>
								</TableRow>
							))}
						</TableHeader>
						<TableBody>
							{table.getRowModel().rows.length ? (
								table.getRowModel().rows.map(row => (
									<TableRow
										key={row.id}
										data-state={row.getIsSelected() && "selected"}
										className="hover:bg-muted/50 transition-colors last:border-b-0 even:bg-white odd:bg-gray-50"
									>
										{row.getVisibleCells().map(cell => (
											<SortableContext key={cell.id} items={columnOrder} strategy={horizontalListSortingStrategy}>
												<DragAlongCell key={cell.id} cell={cell} />
											</SortableContext>
										))}
									</TableRow>
								))
							) : (
								<TableRow>
									<TableCell colSpan={columns.length} className="h-24 text-center">
										Nenhum resultado.
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</div>
			</DndContext>
		</>
	);
}
