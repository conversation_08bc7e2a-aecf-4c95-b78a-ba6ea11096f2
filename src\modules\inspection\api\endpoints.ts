import { buildQueryParams } from "@/shared/lib/utils/url-query-params";
import { IHookPaginatedProps } from "../../../shared/types/pagination/types";
import { IFindAllFieldsParamns } from "../hooks/fields/list/find-all.hook";

import { IFindAllMeasureaParamns } from "../hooks/measures/list/find-all.hook";
import { ICellByProductTypeParamsDto } from "../types/cell-by-product-type/dtos/find-all.dto";
import { ICellComponentsParamsDto } from "../types/cell-components/dtos/find-all.dto";

export const INSPECTION_FORM_ENDPOINTS = {
	CREATE: "/inspection/form",
	FIND_ALL: (params: IHookPaginatedProps) =>
		buildQueryParams("/inspection/form", {
			...params,
		}),
	UPDATE: (id: string) => `/inspection/form/${id}`,
	DELETE: (id: string) => `/inspection/form/${id}`,
	CLONE: (id: string) => `/inspection/form/${id}/clone`,
	FIND_BY_ID: (id: string) => `/inspection/form/${id}`,
};

export const INSPECTION_FORMS_LINKS_ENDPOINTS = {
	FIND_ALL: (params: IHookPaginatedProps) =>
		buildQueryParams("/inspection/form/link", {
			...params,
		}),
	CREATE: "/inspection/form/link",
	DELETE: (id: string) => `/inspection/form/link/${id}`,
};

export const MEASURES_ENDPOINTS = {
	FIND_ALL: (params: IFindAllMeasureaParamns) => buildQueryParams("/inspection/measure", { ...params }),
	CREATE: "/inspection/measure",
	DELETE: (id: string) => `/inspection/measure/${id}`,
};

export const FIELDS_ENDPOINTS = {
	CREATE: "/inspection/fields",
	FIND_ALL: (params: IFindAllFieldsParamns) => buildQueryParams("/inspection/fields", { ...params }),
	DELETE: (id: string) => `/inspection/fields/${id}`,
};

export const CELL_COMPONENTS_ENDPOINTS = {
	FIND_ALL: (params: ICellComponentsParamsDto) => buildQueryParams("/inspection/cell-production-by-components", { ...params }),
	CREATE: "/inspection/cell-production-by-components",
	DELETE: (id: string) => `/inspection/cell-production-by-component/${id}`,
};

export const CELL_BY_PRODUCT_TYPE_ENDPOINTS = {
	CREATE: "/inspection/cell-production-by-product-type",
	FIND_ALL: (params: ICellByProductTypeParamsDto) => buildQueryParams("/inspection/cell-production-by-product-type", { ...params }),
	DELETE: (id: string) => `/inspection/cell-production-by-product-type/${id}`,
};
