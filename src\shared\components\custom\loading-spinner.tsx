import { cn } from "@/shared/lib/shadcn/utils";
import { Loader2 } from "lucide-react";

interface LoadingSpinnerProps {
	size?: "sm" | "md" | "lg";
	className?: string;
	message?: string;
	variant?: "inline" | "centered" | "overlay";
}

const sizeMap = {
	sm: "h-4 w-4",
	md: "h-6 w-6",
	lg: "h-8 w-8",
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = "md", className, message, variant = "inline" }) => {
	const spinner = <Loader2 className={cn("text-primary animate-spin", sizeMap[size], className)} />;

	if (variant === "inline") {
		return (
			<div className="flex items-center gap-2">
				{spinner}
				{message && <span className="text-muted-foreground text-sm">{message}</span>}
			</div>
		);
	}

	if (variant === "centered") {
		return (
			<div className="flex flex-col items-center justify-center space-y-3 py-8">
				{spinner}
				{message && <p className="text-muted-foreground text-center text-sm">{message}</p>}
			</div>
		);
	}

	// overlay variant
	return (
		<div className="bg-background/80 absolute inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
			<div className="bg-card flex flex-col items-center space-y-3 rounded-lg border p-6 shadow-lg">
				{spinner}
				{message && <p className="text-muted-foreground text-center text-sm">{message}</p>}
			</div>
		</div>
	);
};
