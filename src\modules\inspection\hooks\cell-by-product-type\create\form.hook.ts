import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { createCellByProductTypeSchema, TCreateCellByProductTypeForm } from "../../../validators/cell-by-product-type/create.validator";

export const useCreateCellByProductTypeForm = () => {
	return useForm<TCreateCellByProductTypeForm>({
		resolver: zodResolver(createCellByProductTypeSchema),
		defaultValues: {
			cellProductionId: undefined,
			productTypeId: undefined,
		},
	});
};
