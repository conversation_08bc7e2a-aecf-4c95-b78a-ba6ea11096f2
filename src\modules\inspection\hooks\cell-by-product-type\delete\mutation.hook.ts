import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../../core/toast";
import { createDeleteRequest } from "../../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";
import { ApiResponse } from "../../../../../shared/types/requests/request.type";
import { CELL_BY_PRODUCT_TYPE_ENDPOINTS } from "../../../api/endpoints";
import { inspectionKeys } from "../../../constants/query/keys";

export const useDeleteCellByProductTypeMutation = () => {
	const queryClient = useQueryClient();

	const deleteCellByProductTypeMutation = useMutation({
		mutationKey: inspectionKeys.cellByProductType.custom("delete"),
		mutationFn: async (id: string) => {
			const res = await createDeleteRequest<ApiResponse<IMessageGlobalReturn>>(CELL_BY_PRODUCT_TYPE_ENDPOINTS.DELETE(id));
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => inspectionKeys.cellByProductType.invalidateAllLists(queryClient),
	});

	return {
		deleteCellByProductType: (id: string) =>
			toast.promise(deleteCellByProductTypeMutation.mutateAsync(id), {
				loading: "Deletando tipo de produto da célula...",
				success: data => data.data.message ?? "Tipo de produto da célula deletado com sucesso!",
				error: data => data.message ?? "Erro ao excluir o tipo de produto da célula",
			}),
	};
};
