import { QueryClient } from "@tanstack/react-query";

type QueryKeySegment = string | number | Record<string, unknown>;
type Filters = Record<string, unknown> | null;

class SmartQueryKeys {
	private readonly base: string[];

	constructor(domain: string) {
		this.base = [domain];
	}

	// Chaves básicas
	all(): string[] {
		return [...this.base];
	}

	detail(id: string): string[] {
		if (!id) throw new Error(`ID é obrigatório para chave de detalhe`);
		return [...this.base, "detail", id];
	}

	list(filters: Filters = null): QueryKeySegment[] {
		return filters ? [...this.base, "list", filters] : [...this.base, "list"];
	}

	// Invalida todas as queries relacionadas a esse domínio
	async invalidateAll(queryClient: QueryClient) {
		return queryClient
			.invalidateQueries({
				queryKey: this.base,
				exact: false,
			})
			.then(() => {
				console.log(`Todas as queries invalidadas: ${this.base.join(", ")}`);
			})
			.catch(error => {
				console.error(`Erro ao invalidar queries: ${error.message}`);
			});
	}

	refetchAll(queryClient: QueryClient) {
		console.log("Está refetchando todas as queries", this.base);

		return queryClient.refetchQueries({
			queryKey: this.base,
			exact: false,
		});
	}

	// Invalida todas as queries de listas, menos de detalhes
	invalidateAllLists(queryClient: QueryClient) {
		return queryClient.invalidateQueries({
			queryKey: [...this.base, "list"],
			exact: false,
		});
	}

	// Invalida uma query de lista específica
	invalidateList(queryClient: QueryClient, filters: Filters = null) {
		return queryClient.invalidateQueries({
			queryKey: this.list(filters),
			exact: !!filters,
		});
	}

	// Método customizado para casos específicos
	custom(...segments: QueryKeySegment[]): QueryKeySegment[] {
		return [...this.base, ...segments];
	}

	// Força um refresh completo - remove do cache e invalida
	async forceRefresh(queryClient: QueryClient) {
		await queryClient.removeQueries({
			queryKey: this.base,
			exact: false,
		});
		return this.invalidateAll(queryClient);
	}
}

export default SmartQueryKeys;
