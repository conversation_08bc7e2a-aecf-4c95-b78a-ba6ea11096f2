import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export const useCloneFormMutation = () => {
	const queryClient = useQueryClient();

	const cloneFormMutation = useMutation({
		mutationKey: inspectionKeys.forms.custom("clone"),
		mutationFn: async (id: string) => {
			const response = await createPostRequest<IMessageGlobalReturn>(INSPECTION_FORM_ENDPOINTS.CLONE(id));
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: () => inspectionKeys.forms.invalidateAll(queryClient),
	});

	return {
		cloneForm: (id: string) =>
			toast.promise(cloneFormMutation.mutateAsync(id), {
				loading: "Clonando formulário...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
