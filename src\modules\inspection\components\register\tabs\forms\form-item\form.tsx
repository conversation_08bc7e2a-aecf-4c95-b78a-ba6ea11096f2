import { ICreateForm } from "@/modules/inspection/validators/form/create";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Textarea } from "@/shared/components/shadcn/textarea";
import { useIsMobile } from "@/shared/hooks/shadcn/use-mobile";
import { X } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { GenericSearchSelect } from "../../../../../../../shared/components/custom/generic-search-select";
import { useFindAllUser } from "../../../../../../user/hooks/list/find-all.hook";
import { SmartSubmitButton } from "./form-fields/fields-error-wrapper/smart-submit-button";
import { TableFields } from "./form-fields/table";
import { MobileFieldsCards } from "./mobile";

interface IFormCreateFormProps {
	mode: "create" | "edit" | "view";
	onClose: () => void;
	methods: UseFormReturn<ICreateForm>;
	onSubmit: (data: ICreateForm) => void;
}

export const requiredLabel = (label: string) => (
	<>
		{label} <span className="text-red-500">*</span>
	</>
);

export const FormCreateForm: React.FC<IFormCreateFormProps> = ({ onClose, methods, onSubmit, mode }) => {
	const isMobile = useIsMobile();
	const handleFormSubmit = (data: ICreateForm) => onSubmit(data);

	return (
		<Form {...methods}>
			<form onSubmit={methods.handleSubmit(handleFormSubmit)} className={`flex flex-col gap-4`}>
				<FormField
					control={methods.control}
					name="title"
					render={({ field }) => (
						<FormItem>
							<FormLabel>{requiredLabel("Título")}</FormLabel>
							<FormControl>
								<Input
									placeholder="Digite seu título"
									{...field}
									disabled={mode === "view"}
									className={mode === "view" ? "cursor-default bg-gray-100" : ""}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={methods.control}
					name="text"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Descrição</FormLabel>
							<FormControl>
								<Textarea
									disabled={mode === "view"}
									className={mode === "view" ? "cursor-default bg-gray-100" : ""}
									placeholder="Digite uma descrição para o formulário"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
					<FormField
						control={methods.control}
						name="nomenclature"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{requiredLabel("Nomenclatura")}</FormLabel>
								<FormControl>
									<Input
										placeholder="Digite a nomenclatura"
										{...field}
										disabled={mode === "view"}
										className={mode === "view" ? "cursor-default bg-gray-100" : ""}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={methods.control}
						name="developer"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{requiredLabel("Elaborador")}</FormLabel>
								<FormControl>
									<GenericSearchSelect
										value={field.value}
										useDataHook={useFindAllUser}
										onChange={value => field.onChange(value)}
										placeholder="Selecione..."
										searchPlaceholder="Buscar elaborador..."
										loadingText="Carregando..."
										emptyText="Nenhum elaborador encontrado."
										width="w-full"
										disabled={mode === "view"}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={methods.control}
						name="approver"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{requiredLabel("Aprovador")}</FormLabel>
								<FormControl>
									<GenericSearchSelect
										value={field.value}
										useDataHook={useFindAllUser}
										onChange={value => field.onChange(value)}
										placeholder="Selecione..."
										searchPlaceholder="Buscar aprovador..."
										loadingText="Carregando..."
										emptyText="Nenhum aprovador encontrado."
										width="w-full"
										disabled={mode === "view"}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
				{isMobile ? <MobileFieldsCards mode={mode} /> : <TableFields mode={mode} />}

				{mode !== "view" && (
					<div className="flex justify-end gap-2 pt-4">
						<Button type="button" variant="outline" onClick={onClose}>
							<X className="mr-2" /> Cancelar
						</Button>
						<SmartSubmitButton mode={mode} isFormValid={methods.formState.isValid} />
					</div>
				)}
			</form>
		</Form>
	);
};
