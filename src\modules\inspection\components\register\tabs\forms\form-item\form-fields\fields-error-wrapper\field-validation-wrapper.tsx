import { useFieldValidation, useFieldValidationStyles } from "@/modules/inspection/hooks/form/error-fields-wrapper/use-field-validation.hook";
import { AlertCircle } from "lucide-react";
import React from "react";
import { cn } from "../../../../../../../../../shared/lib/shadcn/utils";
import "./field-validation.css";
import { ValidationTooltip } from "./validation-tooltip";

interface FieldValidationWrapperProps {
	tempId: string;
	children: React.ReactNode;
	className?: string;
	showValidationIcon?: boolean;
	showErrorMessage?: boolean;
	showTooltip?: boolean;
}

export const FieldValidationWrapper: React.FC<FieldValidationWrapperProps> = ({
	tempId,
	children,
	className,
	showErrorMessage = true,
	showTooltip = false,
}) => {
	const validation = useFieldValidation(tempId);
	const styles = useFieldValidationStyles(tempId);

	const content = (
		<div
			className={cn(styles.container, className, "rounded-controls")}
			data-field-id={tempId}
			data-has-errors={validation.hasErrors}
			data-is-valid={validation.isValid}
			style={
				{
					"--validation-border-color": validation.hasErrors ? "rgb(252, 165, 165)" : validation.isValid ? "rgb(134, 239, 172)" : undefined,
					"--validation-ring-color": validation.hasErrors ? "rgba(239, 68, 68, 0.2)" : validation.isValid ? "rgba(34, 197, 94, 0.2)" : undefined,
				} as React.CSSProperties
			}
		>
			<div className="relative">{children}</div>
			{showErrorMessage && validation.hasErrors && validation.firstError && (
				<div className={cn("mt-1 flex items-center gap-1", styles.error)}>
					<AlertCircle className="h-3 w-3 flex-shrink-0" />
					<span className="text-xs">{validation.firstError}</span>
				</div>
			)}
		</div>
	);

	if (showTooltip && (validation.hasErrors || validation.isValid)) {
		return (
			<ValidationTooltip tempId={tempId} showOnValid={true}>
				{content}
			</ValidationTooltip>
		);
	}

	return content;
};

export const useValidatedFieldProps = (tempId: string) => {
	const styles = useFieldValidationStyles(tempId);
	const validation = useFieldValidation(tempId);

	return {
		className: styles.input,
		"data-field-id": tempId,
		"aria-invalid": validation.hasErrors,
		"aria-describedby": validation.hasErrors ? `${tempId}-error` : undefined,
	};
};
