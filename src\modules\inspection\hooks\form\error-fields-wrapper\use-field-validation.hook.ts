import { getFieldValidationErrorsAtom, isFieldValidAtom } from "@/modules/inspection/atoms/forms/fields/field-validation.atom";
import { useAtomValue } from "jotai";
import { useMemo } from "react";

export interface FieldValidationState {
	hasErrors: boolean;
	errors: Record<string, string[]>;
	isValid: boolean;
	errorMessages: string[];
	firstError?: string;
}

export const useFieldValidation = (tempId: string): FieldValidationState => {
	const getFieldErrors = useAtomValue(getFieldValidationErrorsAtom);
	const isFieldValid = useAtomValue(isFieldValidAtom);

	const validationState = useMemo(() => {
		const errors = getFieldErrors(tempId);
		const isValid = isFieldValid(tempId);
		const hasErrors = Object.keys(errors).length > 0;
		const errorMessages = Object.values(errors).flat();
		const firstError = errorMessages[0];

		return {
			hasErrors,
			errors,
			isValid,
			errorMessages,
			firstError,
		};
	}, [getFieldErrors, isFieldValid, tempId]);

	return validationState;
};

export const useFieldValidationStyles = (tempId: string) => {
	const { hasErrors, isValid } = useFieldValidation(tempId);

	return useMemo(() => {
		const baseClasses = "transition-colors duration-200";

		if (hasErrors) {
			return {
				container: `${baseClasses} border-red-200 bg-red-50/50`,
				input: "border-red-300 focus:border-red-500 focus:ring-red-500/20",
				label: "text-red-700",
				error: "text-red-600 text-sm mt-1",
			};
		}

		if (isValid) {
			return {
				container: `${baseClasses} border-green-200 bg-green-50/30`,
				input: "border-green-300 focus:border-green-500 focus:ring-green-500/20",
				label: "text-green-700",
				error: "",
			};
		}

		return {
			container: baseClasses,
			input: "",
			label: "",
			error: "",
		};
	}, [hasErrors, isValid]);
};
