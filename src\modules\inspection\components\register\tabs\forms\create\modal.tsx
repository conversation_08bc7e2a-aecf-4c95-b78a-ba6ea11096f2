import { fieldsGroups<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/group.atom";
import { useCreateForm } from "@/modules/inspection/hooks/form/create/form.hook";
import { useCreateFormMutation } from "@/modules/inspection/hooks/form/create/mutation.hook";
import { InspectionFormToCreateMapper } from "@/modules/inspection/lib/mappers/forms/form-to-create.mapper";
import { ICreateForm } from "@/modules/inspection/validators/form/create";
import { Modal } from "@/shared/components/custom/modal";
import { useAtomValue, useSetAtom } from "jotai";
import { FormCreateForm } from "../form-item/form";

interface IModalCreateFormProps {
	isOpen: boolean;
	onClose: () => void;
}

export const ModalCreateForm: React.FC<IModalCreateFormProps> = ({ isOpen, onClose }) => {
	const { methods, ...formActions } = useCreateForm();
	const fields = useAtomValue(fieldsGroupsAtom);
	const { createForm } = useCreateFormMutation();
	const setFieldsGroups = useSetAtom(fieldsGroupsAtom);

	const handleClose = () => {
		setFieldsGroups([]);
		formActions.resetForm();
		onClose();
	};

	const handleSubmit = (data: ICreateForm) =>
		createForm(InspectionFormToCreateMapper.map(data, fields)).then(() => {
			handleClose();
		});

	return (
		<Modal
			isOpen={isOpen}
			onClose={handleClose}
			className="!h-[95vh] !w-full !max-w-[95vw] md:!h-auto md:!w-[1400px] md:!max-w-none"
			title="Cadastro de Formulário"
		>
			<FormCreateForm mode="create" onClose={handleClose} methods={methods} onSubmit={handleSubmit} {...formActions} />
		</Modal>
	);
};
