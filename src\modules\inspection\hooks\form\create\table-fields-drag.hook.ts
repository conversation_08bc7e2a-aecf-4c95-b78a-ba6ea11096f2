import { reorderItemsInGroup<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/field-actions.atom";
import { fieldsGroupsAtom } from "@/modules/inspection/atoms/forms/fields/group.atom";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { DragEndEvent, KeyboardSensor, MouseSensor, TouchSensor, useSensor, useSensors } from "@dnd-kit/core";
import { useAtomValue, useSetAtom } from "jotai";
import { useId, useMemo } from "react";

export const useTableFieldsDrag = (items: ICreateFieldForm[], groupId: string) => {
	const sortableFieldId = useId();
	const setReorderedFields = useSetAtom(reorderItemsInGroupAtom);
	const inspectionFieldGroups = useAtomValue(fieldsGroupsAtom);
	const sensorsFields = useSensors(useSensor(MouseSensor, {}), useSensor(TouchSensor, {}), useSensor(KeyboardSensor, {}));

	const currentItems = useMemo(() => {
		const group = inspectionFieldGroups.find(g => g.tempId === groupId);
		return group ? group.items : items;
	}, [inspectionFieldGroups, groupId, items]);
	const dataIdsFields = useMemo(() => currentItems.map((item: ICreateFieldForm) => item.tempId), [currentItems]);

	const handleDragEndFieldsGroup = (event: DragEndEvent) => {
		const { active, over } = event;
		if (active && over && active.id !== over.id) {
			const activeId = String(active.id);
			const overId = String(over.id);

			const fromIndex = currentItems.findIndex((item: ICreateFieldForm) => item.tempId === activeId);
			const toIndex = currentItems.findIndex((item: ICreateFieldForm) => item.tempId === overId);
			if (fromIndex !== -1 && toIndex !== -1) setReorderedFields({ groupId, fromIndex, toIndex });
		}
	};

	return {
		sensorsFields,
		handleDragEndFieldsGroup,
		sortableFieldId,
		dataIdsFields,
		currentItems,
	};
};
