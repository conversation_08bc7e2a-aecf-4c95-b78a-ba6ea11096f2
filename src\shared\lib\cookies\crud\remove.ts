"use server";

import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { cookies } from "next/headers";
import { COOKIE_ERROR_MESSAGES, COOKIE_SUCCESS_MESSAGES, HTTP_STATUS } from "../constants";

interface IRemoveCookieProps {
	name: string;
}

export const removeCookie = async ({ name }: IRemoveCookieProps): Promise<ApiResponse<IMessageGlobalReturn>> => {
	try {
		const cookieStore = await cookies();
		if (!cookieStore.has(name)) {
			return {
				success: false,
				data: { message: COOKIE_ERROR_MESSAGES.NOT_FOUND(name) },
				status: HTTP_STATUS.NOT_FOUND,
			};
		}
		cookieStore.delete(name);
		return {
			success: true,
			data: { message: COOKIE_SUCCESS_MESSAGES.REMOVED(name) },
			status: HTTP_STATUS.OK,
		};
	} catch (error: unknown) {
		const message = COOKIE_ERROR_MESSAGES.GENERIC_ERROR(error instanceof Error ? error.message : "Erro desconhecido");
		return {
			success: false,
			data: { message },
			status: HTTP_STATUS.INTERNAL_ERROR,
		};
	}
};
