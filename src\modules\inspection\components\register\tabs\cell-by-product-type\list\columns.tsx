import { ColumnDef } from "@tanstack/react-table";
import { IInspectionCellByProductType } from "../../../../../types/cell-by-product-type/dtos/find-all.dto";
import { CellByProductTypeActions } from "./actions";

export const inspectionCellByProductTypeColumns: ColumnDef<IInspectionCellByProductType>[] = [
	{
		accessorKey: "cellName",
		header: () => <div className="text-center font-semibold">Nome <PERSON> Célula</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-center px-2">
				<span className="text-primary max-w-[200px] truncate text-center font-medium">{row.original.cellName}</span>
			</div>
		),
	},
	{
		accessorKey: "productTypeName",
		header: () => <div className="text-center font-semibold">Tipo de Produto</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-center px-2">
				<span className="text-primary max-w-[200px] truncate text-center font-medium">{row.original.productTypeName}</span>
			</div>
		),
	},
	{
		accessorKey: "actions",
		header: () => <div className="text-center font-semibold">Ações</div>,
		cell: ({ row }) => (
			<div className="flex items-center justify-center px-2">
				<CellByProductTypeActions id={String(row.original.id)} name={row.original.cellName} />
			</div>
		),
	},
];
