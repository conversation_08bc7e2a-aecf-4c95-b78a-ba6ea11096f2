import { ElementType } from "react";
import { AppAbility, IDefineCaslPermission } from "../permissions/types";

export interface ICreateBaseItemPathManager {
	name: string;
	description: string;
	id: string;
	icon: ElementType;
	route: ICreateRoutePathManager;
}

export interface ICreateRoutePathManager {
	path: string;
	active: boolean;
	subPaths?: Record<string, ICreateRoutePathManager>;
	params?: Record<string, string>;
}

export interface IDefinePermissionPathManager {
	requiredPermissions: IDefineCaslPermission[];
}

export interface IVisibilityOnMenuPathManager {
	visibleOnMobile: boolean;
	visibleOnMenu: boolean;
}

export interface ICreateItemPathManager extends ICreateBaseItemPathManager, IDefinePermissionPathManager, IVisibilityOnMenuPathManager {
	subItems?: ICreateItemPathManager[];
}

export interface ICreateItemGroupPathManager {
	title: string;
	items: ICreateItemPathManager[];
}

export interface IPathManagerService {
	getItemById(id: string): ICreateItemPathManager | undefined;
	hasPermission(item: ICreateItemPathManager, ability: AppAbility): boolean;
	getMenuForUser(ability: AppAbility): ICreateItemGroupPathManager[];
	getItemByPath(path: string, usePartialMatch?: boolean): ICreateItemPathManager | undefined;
}
