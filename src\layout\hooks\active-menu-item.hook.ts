"use client";

import { pathItems } from "@/config/path-manager/items";
import { pathService } from "@/config/path-manager/service";
import { ICreateItemGroupPathManager, ICreateItemPathManager } from "@/config/path-manager/types";
import { useNavigatePaths } from "@/shared/hooks/utils";
import { useCallback, useEffect, useMemo, useState } from "react";

interface IActiveMenuState {
	activeItemId: string | null;
	activeSubItemId: string | null;
	parentOfActiveSubItemId: string | null;
	activeGroup: ICreateItemGroupPathManager | null;
	currentItem: ICreateItemPathManager | null;
}

interface IUseActiveMenuItemReturn {
	activeItemId: string | null;
	activeSubItemId: string | null;
	parentOfActiveSubItemId: string | null;
	activeGroup: ICreateItemGroupPathManager | null;
	currentItem: ICreateItemPathManager | null;
	isMenuItemActive: (id: string) => boolean;
	isSubMenuItemActive: (id: string) => boolean;
}

const createInitialState = (): IActiveMenuState => ({
	activeItemId: null,
	activeSubItemId: null,
	parentOfActiveSubItemId: null,
	activeGroup: null,
	currentItem: null,
});

const findMenuItemDetails = (pathname: string): IActiveMenuState => {
	if (pathname === "/") {
		return {
			activeItemId: "home",
			activeSubItemId: null,
			parentOfActiveSubItemId: null,
			activeGroup: pathItems.find(group => group.items.some(item => item.id === "home")) || null,
			currentItem: pathService.getItemById("home") || null,
		};
	}
	const currentItem = pathService.getItemByPath(pathname);
	if (!currentItem) return createInitialState();

	for (const group of pathItems) {
		for (const item of group.items) {
			if (item.id === currentItem.id) {
				return {
					activeItemId: item.id,
					activeSubItemId: null,
					parentOfActiveSubItemId: null,
					activeGroup: group,
					currentItem: item,
				};
			}

			if (item.subItems?.some(sub => sub.id === currentItem.id)) {
				return {
					activeItemId: item.id,
					activeSubItemId: currentItem.id,
					parentOfActiveSubItemId: item.id,
					activeGroup: group,
					currentItem: currentItem,
				};
			}
		}
	}

	return createInitialState();
};

export const useActiveMenuItem = (): IUseActiveMenuItemReturn => {
	const { pathname } = useNavigatePaths();
	const [activeMenu, setActiveMenu] = useState<IActiveMenuState>(createInitialState);

	const updateActiveItems = useCallback(() => {
		const newState = findMenuItemDetails(pathname);
		setActiveMenu(newState);
	}, [pathname]);

	useEffect(() => {
		updateActiveItems();
	}, [updateActiveItems]);

	const isMenuItemActive = useCallback((id: string) => activeMenu.activeItemId === id, [activeMenu.activeItemId]);
	const isSubMenuItemActive = useCallback((id: string) => activeMenu.activeSubItemId === id, [activeMenu.activeSubItemId]);

	return useMemo(
		() => ({
			activeItemId: activeMenu.activeItemId,
			activeSubItemId: activeMenu.activeSubItemId,
			parentOfActiveSubItemId: activeMenu.parentOfActiveSubItemId,
			activeGroup: activeMenu.activeGroup,
			currentItem: activeMenu.currentItem,
			isMenuItemActive,
			isSubMenuItemActive,
		}),
		[activeMenu, isMenuItemActive, isSubMenuItemActive]
	);
};
