import { toast } from "@/core/toast";
import { MEASURES_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { ICreateMeasuresDTO } from "@/modules/inspection/types/measures/create-measures.dto";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export function useCreateMeasuresMutation() {
	const queryClient = useQueryClient();

	const createMeasuresMutations = useMutation({
		mutationKey: inspectionKeys.measures.custom("create"),
		mutationFn: async (form: ICreateMeasuresDTO) => {
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(MEASURES_ENDPOINTS.CREATE, form);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => inspectionKeys.measures.invalidateAll(queryClient),
	});

	return {
		createMeasures: (form: ICreateMeasuresDTO) =>
			toast.promise(createMeasuresMutations.mutateAsync(form), {
				loading: "Criando medida...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
}
