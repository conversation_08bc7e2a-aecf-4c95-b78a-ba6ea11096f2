import { Skeleton } from "@/shared/components/shadcn/skeleton";
import { cn } from "@/shared/lib/shadcn/utils";
import { Loader2 } from "lucide-react";

interface FormLoadingProps {
	className?: string;
	message?: string;
	variant?: "skeleton" | "spinner" | "full";
}

export const FormLoading: React.FC<FormLoadingProps> = ({ className, message = "Carregando formulário...", variant = "full" }) => {
	if (variant === "skeleton") {
		return (
			<div className={cn("space-y-6 p-6", className)}>
				<Skeleton className="mb-2 h-8 w-3/4" />
				<Skeleton className="mb-4 h-4 w-1/2" />
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					{Array.from({ length: 6 }).map((_, i) => (
						<div key={i} className="space-y-2">
							<Skeleton className="h-4 w-24" />
							<Skeleton className="h-10 w-full" />
						</div>
					))}
				</div>
				<div className="flex justify-end gap-3 pt-4">
					<Skeleton className="h-10 w-24" />
					<Skeleton className="h-10 w-32" />
				</div>
			</div>
		);
	}

	if (variant === "spinner") {
		return (
			<div className={cn("flex items-center justify-center p-8", className)}>
				<Loader2 className="text-primary mb-2 h-8 w-8 animate-spin" />
				<p className="text-muted-foreground text-sm">{message}</p>
			</div>
		);
	}

	return (
		<div className={cn("relative", className)}>
			<div className="absolute inset-0 z-10 flex items-center justify-center backdrop-blur-sm">
				<div className="bg-card flex flex-col items-center rounded-lg border p-6 shadow-lg">
					<Loader2 className="text-primary mb-2 h-8 w-8 animate-spin" />
					<p className="text-sm font-medium">{message}</p>
					<p className="text-muted-foreground text-xs">Por favor, aguarde...</p>
				</div>
			</div>
			<div className="pointer-events-none space-y-6 p-6 opacity-50 select-none">
				<Skeleton className="mb-2 h-8 w-3/4" />
				<Skeleton className="mb-4 h-4 w-1/2" />
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					{Array.from({ length: 6 }).map((_, i) => (
						<div key={i} className="space-y-2">
							<Skeleton className="h-4 w-24" />
							<Skeleton className="h-10 w-full" />
						</div>
					))}
				</div>
				<div className="flex justify-end gap-3 pt-4">
					<Skeleton className="h-10 w-24" />
					<Skeleton className="h-10 w-32" />
				</div>
			</div>
		</div>
	);
};
