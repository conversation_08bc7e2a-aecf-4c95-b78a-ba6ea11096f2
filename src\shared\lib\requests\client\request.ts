import { MAX_CONCURRENT_REQUESTS } from "../constants";

export class RequestManager {
	private readonly pendingRequests = new Map<string, Promise<unknown>>();
	private readonly abortControllers = new Map<string, AbortController>();
	private readonly requestQueue: Array<{
		fn: () => Promise<unknown>;
		resolve: (value: unknown) => void;
		reject: (reason?: unknown) => void;
	}> = [];
	private concurrentRequests = 0;

	constructor(private readonly maxConcurrentRequests: number = MAX_CONCURRENT_REQUESTS) {}

	createAbortController(requestId: string): AbortController {
		const controller = new AbortController();
		this.abortControllers.set(requestId, controller);
		return controller;
	}

	registerPendingRequest<T>(requestKey: string, promise: Promise<T>): void {
		this.pendingRequests.set(
			requestKey,
			promise.finally(() => {
				this.pendingRequests.delete(requestKey);
				this.abortControllers.delete(requestKey);
			}),
		);
	}

	getPendingRequest(requestKey: string): Promise<unknown> | undefined {
		return this.pendingRequests.get(requestKey);
	}

	cancelRequest(requestKey: string): boolean {
		const controller = this.abortControllers.get(requestKey);
		if (!controller) return false;
		controller.abort();
		this.abortControllers.delete(requestKey);
		this.pendingRequests.delete(requestKey);
		return true;
	}

	async enqueue<T>(fn: () => Promise<T>): Promise<T> {
		if (this.concurrentRequests < this.maxConcurrentRequests) return this.executeRequest(fn);
		return new Promise<T>((resolve, reject) => {
			this.requestQueue.push({
				fn: fn as () => Promise<unknown>,
				resolve: resolve as (value: unknown) => void,
				reject,
			});
		});
	}

	private async executeRequest<T>(fn: () => Promise<T>): Promise<T> {
		this.concurrentRequests++;
		try {
			const result = await fn();
			this.processQueue();
			return result;
		} catch (error) {
			this.processQueue();
			throw error;
		} finally {
			this.concurrentRequests--;
		}
	}

	private processQueue(): void {
		if (this.requestQueue.length === 0 || this.concurrentRequests >= this.maxConcurrentRequests) return;
		const { fn, resolve, reject } = this.requestQueue.shift()!;
		this.executeRequest(fn).then(resolve).catch(reject);
	}

	cancelAll(): void {
		this.abortControllers.forEach(controller => controller.abort());
		this.abortControllers.clear();
		this.pendingRequests.clear();
		this.requestQueue.length = 0;
	}
}
