import useCreateCollabSectorTypeForm from "@/modules/inspection/hooks/collaborator-by-sector/create/collab-sector.hook";
import { TCreateCollabSectorSchema } from "@/modules/inspection/validators/collaborator-by-sector/create";
import { Modal } from "@/shared/components/custom/modal";
import FormCreateMeasures from "./form";

interface IModalCreateFormCollabSector {
	isOpen: boolean;
	onClose: () => void;
}

export default function ModalCreateCollabSector({ isOpen, onClose }: IModalCreateFormCollabSector) {
	const { methods } = useCreateCollabSectorTypeForm();
	// const { createMeasures } = useCreateMeasuresMutation();

	function handleSubmit(data: TCreateCollabSectorSchema) {
		const payload = {
			...data,
			pin: data.pin,
			collab: data.collab.id,
			sector: data.sector.id,
		};
		// createMeasures(payload);
		console.log(payload);
		onClose();
		methods.reset();
	}

	return (
		<Modal isOpen={isOpen} onClose={onClose} className="!w-[500px] !max-w-none" title="Vincular colaborador no setor">
			<FormCreateMeasures methods={methods} onSubmit={handleSubmit} onClose={onClose} />
		</Modal>
	);
}
