import { useCallback, useState } from "react";

export function useModal(initialState: boolean = false) {
	const [isOpen, setIsOpen] = useState(initialState);

	const openModal = useCallback(() => setIsOpen(true), []);
	const closeModal = useCallback(() => setIsOpen(false), []);
	const toggleModal = useCallback(() => setIsOpen(prev => !prev), []);

	return {
		isOpen,
		setIsOpen,
		openModal,
		closeModal,
		toggleModal,
	};
}
