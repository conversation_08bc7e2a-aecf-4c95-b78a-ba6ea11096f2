import RefreshService from "@/core/auth/services/refresh-service";
import { AxiosInstance } from "axios";

export const setupRefreshInterceptor = (instance: AxiosInstance): void => {
	const refreshService = new RefreshService(instance);

	instance.interceptors.response.use(
		response => response,
		async error => {
			const originalRequest = error.config;
			if (
				error.response?.status === 401 &&
				!originalRequest._retry &&
				originalRequest.url &&
				!refreshService.isNonRefreshableEndpoint(originalRequest.url)
			) {
				try {
					return await refreshService.handleTokenRefresh(originalRequest);
				} catch (refreshError) {
					if (refreshError instanceof Error) return Promise.reject(refreshError);
					return Promise.reject(new Error(String(refreshError)));
				}
			}
			return Promise.reject(error instanceof Error ? error : new Error(String(error)));
		}
	);
};
