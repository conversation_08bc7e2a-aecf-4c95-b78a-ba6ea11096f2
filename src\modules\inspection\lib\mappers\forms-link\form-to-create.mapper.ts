import { ICreateFormLinkDto } from "../../../types/form-link/dtos/create.dto";
import { TCreateFormLink } from "../../../validators/forms-links/create";

export class InspectionFormsLinkToCreateMapper {
	static map({ form, cell, activity, cellByComponent, cellByProductType }: TCreateFormLink): ICreateFormLinkDto {
		return {
			formId: form.id,
			cellId: cell.id,
			activityId: activity.id,
			cellByComponentId: cellByComponent?.id,
			cellByProductTypeId: cellByProductType?.id,
		};
	}
}
