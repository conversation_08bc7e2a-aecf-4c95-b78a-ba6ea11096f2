import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { IFieldGroup } from "../fields-table/fields-group.type";

export interface IMobileFieldsCardsProps {
	searchTerm?: string;
	onFieldSelect?: (fieldId: string | null) => void;
	onFieldsReorder?: (groupId: string, fromIndex: number, toIndex: number) => void;
	onFieldRemove?: (fieldId: string) => void;
	onGroupRemove?: (groupId: string) => void;
	isLoading?: boolean;
	disabled?: boolean;
	mode?: "create" | "edit" | "view";
}

export interface IFlattenedFieldMobile extends ICreateFieldForm {
	groupId: string;
	groupTitle?: string;
	indexInGroup: number;
	totalInGroup: number;
}

export interface IMobileFieldCardProps {
	field: IFlattenedFieldMobile;
	isFirst: boolean;
	isLast: boolean;
	isSelected: boolean;
	isReordering?: boolean;
	onMoveUp: () => void;
	onMoveDown: () => void;
	onCardClick: () => void;
}

export interface IMobileGroupHeaderProps {
	group: IFieldGroup;
	autoFocus?: boolean;
	onTitleChange?: (groupId: string, title: string) => void;
	onAddItem?: (groupId: string) => void;
	onRemoveGroup?: (groupId: string) => void;
}

export interface IMobileCardAnimationConfig {
	duration?: number;
	stagger?: number;
	useSpring?: boolean;
	springConfig?: {
		stiffness: number;
		damping: number;
	};
}

export interface IMobileFieldsPerformanceMetrics {
	totalFields: number;
	totalGroups: number;
	renderTime?: number;
	isLazyLoading: boolean;
}

export enum MobileFieldActionType {
	MOVE_UP = "MOVE_UP",
	MOVE_DOWN = "MOVE_DOWN",
	DELETE = "DELETE",
	SELECT = "SELECT",
	EDIT = "EDIT",
}

export interface IMobileFieldActionEvent {
	type: MobileFieldActionType;
	fieldId: string;
	groupId: string;
	payload?: Record<string, unknown>;
}
