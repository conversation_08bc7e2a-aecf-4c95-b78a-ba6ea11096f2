"use client";

import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useCallback } from "react";
import { chatErrorAtom } from "../atoms/handlers/error.atom";
import { addMessageAtom } from "../atoms/session/add-message.atom";
import { sessionId<PERSON>tom } from "../atoms/session/info.atom";
import { updateMessageAtom } from "../atoms/session/update-message.atom";
import { sendStreamingMessage } from "../lib/streaming-send-message.lib";
import { chatStreamService } from "../services/streaming/streaming.service";
import {
	isStreamingAtom,
	isPausedAtom,
	currentStreamingMessageIdAtom,
	startStreamingAtom,
	stopStreaming<PERSON>tom,
	pauseStreaming<PERSON>tom,
	resumeStreaming<PERSON>tom,
	resetStreamingAtom,
} from "../atoms/streaming/state.atom";

export const useStreamingManager = () => {
	const currentSessionId = useAtomValue(sessionIdAtom);
	const addMessage = useSet<PERSON>tom(addMessageAtom);
	const updateMessage = useSetAtom(updateMessageAtom);
	const setError = useSetAtom(chatErrorAtom);

	// Streaming state
	const isStreaming = useAtomValue(isStreamingAtom);
	const isPaused = useAtomValue(isPausedAtom);
	const currentMessageId = useAtomValue(currentStreamingMessageIdAtom);

	// Streaming actions
	const startStreaming = useSetAtom(startStreamingAtom);
	const stopStreaming = useSetAtom(stopStreamingAtom);
	const pauseStreaming = useSetAtom(pauseStreamingAtom);
	const resumeStreaming = useSetAtom(resumeStreamingAtom);
	const resetStreaming = useSetAtom(resetStreamingAtom);

	const sendMessage = useCallback(
		async (content: string) => {
			if (!content.trim()) return;
			const sessionId = currentSessionId;
			if (!sessionId) throw new Error("O id da sessão não está disponível");

			// Se já estiver fazendo streaming, pare primeiro
			if (isStreaming) {
				stopStreaming();
			}

			try {
				await sendStreamingMessage({
					sessionId,
					content,
					addMessage,
					onUpdate: updateMessage,
					setError,
					onStreamingStart: messageId => {
						startStreaming(messageId);
					},
				});

				// Finalizar streaming
				stopStreaming();
			} catch (error) {
				console.error("Error sending message:", error);
				stopStreaming();
				setError(error instanceof Error ? error.message : "Erro ao enviar mensagem");
			}
		},
		[addMessage, currentSessionId, setError, updateMessage, isStreaming, stopStreaming, startStreaming],
	);

	const stop = useCallback(() => {
		if (isStreaming) {
			// Abortar o streaming no serviço
			chatStreamService.abortStream();
			stopStreaming();

			// Atualizar a mensagem atual para indicar que foi interrompida
			if (currentMessageId) {
				updateMessage(currentMessageId, {
					content: "Resposta interrompida pelo usuário.",
					isStreaming: false,
					isError: false,
				});
			}
		}
	}, [isStreaming, stopStreaming, currentMessageId, updateMessage]);

	const pause = useCallback(() => {
		if (isStreaming && !isPaused) {
			pauseStreaming();
		}
	}, [isStreaming, isPaused, pauseStreaming]);

	const resume = useCallback(() => {
		if (isStreaming && isPaused) {
			resumeStreaming();
		}
	}, [isStreaming, isPaused, resumeStreaming]);

	const reset = useCallback(() => {
		resetStreaming();
		setError(null);
	}, [resetStreaming, setError]);

	return {
		// Métodos principais
		sendMessage,
		stop,
		pause,
		resume,
		reset,

		// Estado
		isStreaming,
		isPaused,
		currentMessageId,

		// Utilitários
		canSend: !isStreaming && !!currentSessionId,
		canStop: isStreaming,
		canPause: isStreaming && !isPaused,
		canResume: isStreaming && isPaused,
	};
};
