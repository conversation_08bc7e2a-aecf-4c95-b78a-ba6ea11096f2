import { NextRequest, NextResponse } from "next/server";

export async function authMiddleware(request: Readonly<NextRequest>): Promise<NextResponse> {
	const { pathname } = request.nextUrl;
	const accessToken = request.cookies.get("access_token")?.value;
	if (!accessToken) {
		const keycloakLoginUrl = new URL("/auth/login", request.url);
		keycloakLoginUrl.searchParams.set("redirect", pathname);
		return NextResponse.redirect(keycloakLoginUrl, { status: 302 });
	}
	return NextResponse.next();
}
