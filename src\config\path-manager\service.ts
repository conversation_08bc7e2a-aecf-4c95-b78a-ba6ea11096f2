import { AppAbility } from "@/config/permissions/types";
import { pathItems } from "./items";
import { ICreateItemGroupPathManager, ICreateItemPathManager, ICreateRoutePathManager, IPathManagerService } from "./types";

export class createPathManagerService implements IPathManagerService {
	private matchPath(path: string, inputPath: string, exactMatch: boolean = false): boolean {
		const normalizePath = (p: string) => (p.startsWith("/") ? p : `/${p}`);
		const pathSegments = normalizePath(path).split("/").filter(Boolean);
		const inputSegments = normalizePath(inputPath).split("/").filter(Boolean);
		if (exactMatch && pathSegments.length !== inputSegments.length) return false;
		if (!exactMatch && inputSegments.length < pathSegments.length) return false;
		return pathSegments.every((segment, index) => segment.startsWith(":") || segment === inputSegments[index]);
	}

	private isActivePath(path?: ICreateRoutePathManager, visited: Set<string> = new Set()): boolean {
		if (!path) return false;
		const pathKey = path.path;
		if (visited.has(pathKey)) return false;
		visited.add(pathKey);
		if (path.active) return true;
		if (path.subPaths) return Object.values(path.subPaths).some(subPath => this.isActivePath(subPath, visited));
		return false;
	}

	public hasPermission(item: ICreateItemPathManager, ability: AppAbility): boolean {
		if (!item.requiredPermissions || item.requiredPermissions.length === 0) return true;
		return item.requiredPermissions.some(permission => ability.can(permission.action, permission.subject));
	}

	private findExactPath(path: string): ICreateItemPathManager | undefined {
		for (const group of pathItems) {
			for (const item of group.items) {
				if (item.route && this.isActivePath(item.route) && this.matchPath(item.route.path, path, true)) return item;
				if (item.subItems) {
					const subItem = item.subItems.find(
						subItem => subItem.route && this.isActivePath(subItem.route) && this.matchPath(subItem.route.path, path, true)
					);
					if (subItem) return subItem;
				}
			}
		}
		return undefined;
	}

	private pathSimilarity(path: string, inputPath: string): number {
		const pathSegments = path.split("/").filter(segment => segment);
		const inputSegments = inputPath.split("/").filter(segment => segment);
		let matchingSegments = 0;
		const minLength = Math.min(pathSegments.length, inputSegments.length);
		for (let i = 0; i < minLength; i++) {
			if (pathSegments[i] === inputSegments[i] || pathSegments[i].startsWith(":")) matchingSegments++;
			else break;
		}
		return matchingSegments - 0.1 * Math.abs(pathSegments.length - inputSegments.length);
	}

	private findBestPartialMatch(path: string): ICreateItemPathManager | undefined {
		const activePaths: ICreateItemPathManager[] = [];
		pathItems.forEach(group => {
			group.items.forEach(item => {
				if (item.route && this.isActivePath(item.route)) {
					activePaths.push(item);
					if (item.subItems) {
						item.subItems.forEach(subItem => {
							if (subItem.route && this.isActivePath(subItem.route)) activePaths.push(subItem);
						});
					}
				}
			});
		});
		return activePaths.reduce(
			(best, item) => {
				const similarity = this.pathSimilarity(item.route.path, path);
				return similarity > best.similarity ? { item, similarity } : best;
			},
			{ item: undefined as ICreateItemPathManager | undefined, similarity: -Infinity }
		).item;
	}
	private pathCache = new Map<string, ICreateItemPathManager>();

	public getItemByPath(path: string): ICreateItemPathManager | undefined {
		if (this.pathCache.has(path)) return this.pathCache.get(path);
		const result = this.findExactPath(path) || this.findBestPartialMatch(path);
		if (result) this.pathCache.set(path, result);
		return result;
	}

	public getItemById(id: string): ICreateItemPathManager | undefined {
		for (const group of pathItems) {
			for (const item of group.items) {
				if (item.id === id) return item;
				if (item.subItems) {
					const subItem = item.subItems.find(subItem => subItem.id === id);
					if (subItem) return subItem;
				}
			}
		}
		return undefined;
	}

	public getMenuForUser(ability: AppAbility): ICreateItemGroupPathManager[] {
		return pathItems.map(group => ({
			...group,
			items: group.items
				.filter(item => item.route && this.isActivePath(item.route) && item.visibleOnMenu && this.hasPermission(item, ability))
				.map(item => {
					if (!item.subItems) return item;
					return {
						...item,
						subItems: item.subItems.filter(
							subItem =>
								subItem.route && this.isActivePath(subItem.route) && subItem.visibleOnMenu && this.hasPermission(subItem, ability)
						),
					};
				}),
		}));
	}
}

export const pathService = new createPathManagerService();
