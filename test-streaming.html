<!DOCTYPE html>
<html>
<head>
    <title>Teste de Streaming</title>
</head>
<body>
    <h1>Teste de Streaming</h1>
    <button onclick="testStreaming()">Testar Streaming</button>
    <div id="output"></div>

    <script>
        async function testStreaming() {
            const output = document.getElementById('output');
            output.innerHTML = '<p>Iniciando teste de streaming...</p>';
            
            try {
                const response = await fetch('http://**************:10/ai/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream',
                        'Cache-Control': 'no-cache',
                        'Connection': 'keep-alive'
                    },
                    body: JSON.stringify({
                        message: 'Olá, como você está?',
                        sessionId: 'test-session'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;

                    // Processar linhas completas
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.trim()) {
                            output.innerHTML += `<p>Recebido: ${line}</p>`;
                            console.log('Linha recebida:', line);
                        }
                    }
                }

                // Processar buffer restante
                if (buffer.trim()) {
                    output.innerHTML += `<p>Buffer final: ${buffer}</p>`;
                }

            } catch (error) {
                output.innerHTML += `<p style="color: red;">Erro: ${error.message}</p>`;
                console.error('Erro no streaming:', error);
            }
        }
    </script>
</body>
</html>
