import { Skeleton } from "@/shared/components/shadcn/skeleton";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { cn } from "@/shared/lib/shadcn/utils";

interface TableLoadingProps {
	rows?: number;
	columns?: number;
}

export const TableLoading: React.FC<TableLoadingProps> = ({ 
	rows = 5, 
	columns = 4 
}) => {
	return (
		<>
			{Array.from({ length: rows }).map((_, rowIndex) => (
				<TableRow key={rowIndex} className="hover:bg-transparent">
					{Array.from({ length: columns }).map((_, colIndex) => (
						<TableCell key={colIndex} className="p-4">
							<Skeleton 
								className={cn(
									"h-4",
									colIndex === 0 ? "w-24" : colIndex === columns - 1 ? "w-16" : "w-32"
								)} 
							/>
						</TableCell>
					))}
				</TableRow>
			))}
		</>
	);
};

interface TableEmptyStateProps {
	message?: string;
	description?: string;
	action?: React.ReactNode;
}

export const TableEmptyState: React.FC<TableEmptyStateProps> = ({
	message = "Nenhum item encontrado",
	description = "Não há dados para exibir no momento.",
	action
}) => {
	return (
		<TableRow>
			<TableCell colSpan={999} className="h-32">
				<div className="flex flex-col items-center justify-center space-y-3 text-center">
					<div className="rounded-full bg-muted p-3">
						<svg
							className="h-6 w-6 text-muted-foreground"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
							/>
						</svg>
					</div>
					<div className="space-y-1">
						<h3 className="text-sm font-medium">{message}</h3>
						<p className="text-xs text-muted-foreground">{description}</p>
					</div>
					{action && <div className="pt-2">{action}</div>}
				</div>
			</TableCell>
		</TableRow>
	);
};
