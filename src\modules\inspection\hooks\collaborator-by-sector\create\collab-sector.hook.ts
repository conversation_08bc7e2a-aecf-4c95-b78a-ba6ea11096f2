import { createCollabSectorSchema, TCreateCollabSectorSchema } from "@/modules/inspection/validators/collaborator-by-sector/create";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, UseFormReturn } from "react-hook-form";

interface useCreateCollabSectorReturn {
	methods: UseFormReturn<TCreateCollabSectorSchema>;
}

export default function useCreateCollabSectorForm(): useCreateCollabSectorReturn {
	const methods = useForm<TCreateCollabSectorSchema>({
		resolver: zodResolver(createCollabSectorSchema),
		defaultValues: {
			collab: undefined,
			sector: undefined,
			pin: "",
		},
		mode: "onChange",
	});
	return { methods };
}
