import { Button } from "@/shared/components/shadcn/button";
import { TableCell, TableRow } from "@/shared/components/shadcn/table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Row, flexRender } from "@tanstack/react-table";
import { GripVertical } from "lucide-react";
import z from "zod";
import { schema } from "./data-table";

export const DragHandle = ({ id }: { id: number }) => {
	const { attributes, listeners } = useSortable({
		id,
	});

	return (
		<Button {...attributes} {...listeners} variant="ghost" size="icon" className="text-muted-foreground size-7 cursor-grab hover:bg-transparent">
			<GripVertical className="text-muted-foreground size-3" />
			<span className="sr-only">Drag to reorder</span>
		</Button>
	);
};

export function DraggableRow({ row }: { row: Row<z.infer<typeof schema>> }) {
	const { transform, transition, setNodeRef, isDragging } = useSortable({
		id: row.original.id,
	});

	return (
		<TableRow
			data-state={row.getIsSelected() && "selected"}
			data-dragging={isDragging}
			ref={setNodeRef}
			className="relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80"
			style={{
				transform: CSS.Transform.toString(transform),
				transition: transition,
			}}
		>
			{row.getVisibleCells().map(cell => (
				<TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
			))}
		</TableRow>
	);
}
