import { LoadingContainer } from "@/layout/components/container/loading-container";
import { MainContainer } from "@/layout/components/container/main-container";
import { IReactChildrenType } from "@/shared/types/components/react-children.type";
import { Suspense } from "react";

export default function Layout({ children }: IReactChildrenType) {
	return (
		<Suspense fallback={<LoadingContainer />}>
			<MainContainer>{children}</MainContainer>
		</Suspense>
	);
}
