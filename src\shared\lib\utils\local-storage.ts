type LocalStorageValue<T> = T | null;

function isLocalStorageAvailable(): boolean {
	try {
		const testKey = "__ls_test__";
		window.localStorage.setItem(testKey, "1");
		window.localStorage.removeItem(testKey);
		return true;
	} catch {
		return false;
	}
}

export function setItem<T = unknown>(key: string, value: T): void {
	if (!isLocalStorageAvailable()) return;
	try {
		const serialized = JSON.stringify(value);
		window.localStorage.setItem(key, serialized);
	} catch {
		return;
	}
}

export function getItem<T = unknown>(key: string): LocalStorageValue<T> {
	if (!isLocalStorageAvailable()) return null;
	try {
		const item = window.localStorage.getItem(key);
		if (item === null) return null;
		return JSON.parse(item) as T;
	} catch {
		return null;
	}
}

export function removeItem(key: string): void {
	if (!isLocalStorageAvailable()) return;
	try {
		window.localStorage.removeItem(key);
	} catch {
		return;
	}
}

export function clear(): void {
	if (!isLocalStorageAvailable()) return;
	try {
		window.localStorage.clear();
	} catch {
		return;
	}
}
