"use client";
import { addField<PERSON>roup<PERSON>tom, generateEmptyGroupWithItems } from "@/modules/inspection/atoms/forms/fields/group-actions.atom";
import { useMobileFieldsReorder } from "@/modules/inspection/hooks/form/mobile/mobile-fields-reorder.hook";
import { IFieldGroup } from "@/modules/inspection/types/forms/fields-table/fields-group.type";
import { IMobileFieldsCardsProps } from "@/modules/inspection/types/forms/mobile/mobile-fields-cards.type";
import { Button } from "@/shared/components/shadcn/button";
import { AnimatePresence, motion } from "framer-motion";
import { useSetAtom } from "jotai";
import { FileStack, FolderPlus, ListPlus, Search } from "lucide-react";
import React, { useMemo, useState } from "react";
import { MobileGroup } from "./group";

export const MobileFieldsCards: React.FC<IMobileFieldsCardsProps> = ({ searchTerm, mode = "create" }) => {
	const addField = useSetAtom(generateEmptyGroupWithItems);
	const addGroup = useSetAtom(addFieldGroupAtom);
	const [activeId, setActiveId] = useState<string | null>(null);
	const { fieldsGroups, isReordering, filterFields } = useMobileFieldsReorder();

	const filteredFields = useMemo(() => {
		return filterFields(searchTerm || "");
	}, [filterFields, searchTerm]);

	const lastGroupId = fieldsGroups.at(-1)?.tempId;
	const totalFields = fieldsGroups.reduce((acc, group) => acc + group.items.length, 0);
	const totalGroups = fieldsGroups.length;

	return (
		<section className="flex flex-col gap-6 p-1">
			<div className="space-y-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-4">
						<div className="flex items-center gap-2">
							<div className="bg-primary/10 text-primary flex h-8 w-8 items-center justify-center rounded-lg">
								<FileStack className="h-4 w-4" />
							</div>
							<div className="text-sm">
								<span className="font-semibold">{totalFields}</span>
								<span className="text-muted-foreground"> {totalFields === 1 ? "campo" : "campos"}</span>
							</div>
						</div>
						<div className="flex items-center gap-2">
							<div className="bg-secondary/10 text-secondary-foreground flex h-8 w-8 items-center justify-center rounded-lg">
								<FolderPlus className="h-4 w-4" />
							</div>
							<div className="text-sm">
								<span className="font-semibold">{totalGroups}</span>
								<span className="text-muted-foreground"> {totalGroups === 1 ? "grupo" : "grupos"}</span>
							</div>
						</div>
					</div>

					{searchTerm && (
						<div className="flex items-center gap-2 text-sm">
							<Search className="text-muted-foreground h-4 w-4" />
							<span className="text-muted-foreground">Filtrado</span>
						</div>
					)}
				</div>

				<div className="grid grid-cols-2 gap-3">
					<Button
						size="lg"
						className="h-12 gap-3 shadow-sm"
						onClick={e => {
							e.preventDefault();
							addGroup();
						}}
						disabled={mode === "view"}
					>
						<FolderPlus className="h-5 w-5" />
						<div className="flex flex-col items-start">
							<span className="font-medium">Novo Grupo</span>
							<span className="text-xs opacity-90">Organizar campos</span>
						</div>
					</Button>
					<Button
						size="lg"
						variant="outline"
						className="h-12 gap-3 shadow-sm"
						onClick={e => {
							e.preventDefault();
							addField();
						}}
						disabled={mode === "view"}
					>
						<ListPlus className="h-5 w-5" />
						<div className="flex flex-col items-start">
							<span className="font-medium">Novo Campo</span>
							<span className="text-xs opacity-70">Adicionar item</span>
						</div>
					</Button>
				</div>
			</div>

			<div className="space-y-6">
				{fieldsGroups.length > 0 ? (
					<div className={`relative flex flex-col gap-2 transition-opacity duration-200 ${isReordering ? "opacity-50" : "opacity-100"}`}>
						{isReordering && (
							<div className="bg-background/20 absolute inset-0 z-10 flex items-center justify-center rounded-xl backdrop-blur-sm">
								<div className="bg-primary/10 text-primary flex items-center gap-2 rounded-lg px-4 py-2 shadow-lg">
									<motion.div
										animate={{ rotate: 360 }}
										transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
										className="border-primary h-4 w-4 rounded-full border-2 border-t-transparent"
									/>
									<span className="text-sm font-medium">Reordenando...</span>
								</div>
							</div>
						)}
						<AnimatePresence mode="popLayout">
							{fieldsGroups.map((group: IFieldGroup, groupIndex) => {
								const groupFields = filteredFields.filter(field => field.groupId === group.tempId);
								if (searchTerm && groupFields.length === 0) return null;

								return (
									<motion.div
										key={group.tempId}
										layout
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										exit={{ opacity: 0, y: -20 }}
										transition={{ duration: 0.2, delay: groupIndex * 0.05 }}
										className="group"
									>
										<MobileGroup
											group={group}
											isView={mode === "view"}
											autoFocus={group.tempId === lastGroupId}
											setActiveId={setActiveId}
											activeId={activeId}
											groupFields={groupFields}
										/>
									</motion.div>
								);
							})}
						</AnimatePresence>
					</div>
				) : (
					<motion.div
						initial={{ opacity: 0, scale: 0.95 }}
						animate={{ opacity: 1, scale: 1 }}
						transition={{ duration: 0.3 }}
						className="border-muted-foreground/25 from-muted/20 to-muted/10 flex flex-col items-center justify-center rounded-xl border-2 border-dashed bg-gradient-to-br px-6 py-12 text-center"
					>
						<div className="bg-muted/10 mb-4 flex h-16 w-16 items-center justify-center rounded-full">
							<FileStack className="text-muted-foreground h-8 w-8" />
						</div>
						<h3 className="mb-2 text-lg font-semibold">Nenhum campo criado</h3>
						<p className="text-muted-foreground mb-6 max-w-sm text-sm">
							Comece criando um grupo para organizar seus campos ou adicione um campo diretamente.
						</p>
						<div className="flex w-full max-w-xs flex-col gap-2">
							<Button size="lg" onClick={() => addGroup()} className="gap-2">
								<FolderPlus className="h-4 w-4" />
								Criar primeiro grupo
							</Button>
							<Button variant="outline" size="lg" onClick={() => addField()} className="gap-2">
								<ListPlus className="h-4 w-4" />
								Adicionar campo
							</Button>
						</div>
					</motion.div>
				)}
			</div>
		</section>
	);
};
