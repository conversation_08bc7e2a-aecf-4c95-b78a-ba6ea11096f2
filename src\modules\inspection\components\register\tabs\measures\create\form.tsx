import { TCreateMeasures } from "@/modules/inspection/validators/measures/create";
import React from "react";
import { Input } from "@/shared/components/shadcn/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Button } from "@/shared/components/shadcn/button";
import { UseFormReturn } from "react-hook-form";

interface IFormCreateMeasuresProps {
	onClose: () => void;
	methods: UseFormReturn<TCreateMeasures>;
	onSubmit: (data: TCreateMeasures) => void;
}

export default function FormCreateMeasures({ onClose, methods, onSubmit }: IFormCreateMeasuresProps) {
	return (
		<Form {...methods}>
			<form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
				<FormField
					control={methods.control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Nome</FormLabel>
							<FormControl>
								<Input {...field} placeholder="Digite o nome" />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={methods.control}
					name="abbreviation"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Abreviação</FormLabel>
							<FormControl>
								<Input {...field} placeholder="Digite a abreviação" />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="flex justify-end gap-2 pt-4">
					<Button type="button" variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button type="submit">Salvar</Button>
				</div>
			</form>
		</Form>
	);
}
