import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../core/toast";
import { createPostRequest } from "../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { ACTIVITY_ENDPOINTS } from "../../api/endpoints";
import { activityQueryKeys } from "../../constants/query";
import { TCreateActivity } from "../../validators/create";

export const useCreateActivityMutation = () => {
	const queryClient = useQueryClient();

	const createMutation = useMutation({
		mutationKey: activityQueryKeys.custom("create"),
		mutationFn: async (activity: TCreateActivity) => {
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(ACTIVITY_ENDPOINTS.CREATE, activity);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => activityQueryKeys.invalidateAll(queryClient),
	});

	return {
		createActivity: (form: TCreateActivity) =>
			toast.promise(createMutation.mutateAsync(form), {
				loading: "Criando atividade...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
