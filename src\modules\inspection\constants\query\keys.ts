import SmartQueryKeys from "../../../../shared/lib/query/smart-query-keys.lib";

export const inspectionKeys = {
	cellByProductType: new SmartQueryKeys("inspection-cell-by-product-type"),
	forms: new SmartQueryKeys("inspection-forms"),
	formsLink: new SmartQueryKeys("inspection-forms-links"),
	cellByComponents: new SmartQueryKeys("inspection-cell-by-components"),
	fields: new SmartQueryKeys("inspection-fields"),
	measures: new SmartQueryKeys("inspection-measures"),
};
