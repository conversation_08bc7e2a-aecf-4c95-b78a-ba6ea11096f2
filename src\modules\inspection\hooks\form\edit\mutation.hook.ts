import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { IUpdateFormDTO } from "@/modules/inspection/types/forms/dtos/update-form.dto";

import { createPutRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export const useUpdateFormMutation = (onClose: () => void) => {
	const queryClient = useQueryClient();

	const updateFormMutation = useMutation({
		mutationKey: inspectionKeys.forms.custom("update"),
		mutationFn: async ({ form, id }: { form: IUpdateFormDTO; id: string }) => {
			console.log("Updating form:", form);
			const { data, success } = await createPutRequest<IMessageGlobalReturn>(INSPECTION_FORM_ENDPOINTS.UPDATE(id), form);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => {
			inspectionKeys.forms.invalidateAll(queryClient);
			onClose();
		},
	});

	return {
		updateForm: (variables: { form: IUpdateFormDTO; id: string }) =>
			toast.promise(updateFormMutation.mutateAsync(variables), {
				loading: "Atualizando formulário...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
