import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../core/toast";
import { createDeleteRequest } from "../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { SECTOR_ENDPOINTS } from "../../api/endpoints";
import { sectorQueryKeys } from "../../constants/query";

export const useDeleteSectorMutation = () => {
	const queryClient = useQueryClient();

	const createSectorMutation = useMutation({
		mutationKey: sectorQueryKeys.custom("delete"),
		mutationFn: async (id: string) => {
			const res = await createDeleteRequest<IMessageGlobalReturn>(SECTOR_ENDPOINTS.DELETE(id));
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => sectorQueryKeys.invalidateAll(queryClient),
	});

	return {
		deleteSector: (id: string) =>
			toast.promise(createSectorMutation.mutateAsync(id), {
				loading: "Excluindo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
