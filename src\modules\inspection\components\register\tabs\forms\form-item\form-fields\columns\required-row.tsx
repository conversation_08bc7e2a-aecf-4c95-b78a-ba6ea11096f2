import { updateFieldRequired<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/field-values.atom";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { Checkbox } from "@/shared/components/shadcn/checkbox";

import { Row } from "@tanstack/react-table";
import { useSet<PERSON>tom } from "jotai";

export const InspectionFormRequiredRow = ({ row, mode }: { row: Row<ICreateFieldForm>; mode?: "create" | "edit" | "view" }) => {
	const { tempId, required } = row.original;
	const updateField = useSetAtom(updateFieldRequiredAtom);

	return (
		<div className="flex w-full items-center justify-center">
			<Checkbox
				disabled={mode === "view"}
				className="bg-white"
				checked={required === true}
				onCheckedChange={checked => updateField({ tempId, required: !!checked })}
			/>
		</div>
	);
};
