import { z } from "zod";

const entitySchema = z.object({
	id: z.number(),
	name: z.string(),
});

const requiredEntitySchema = (message: string) =>
	z.custom<Entity>(val => val && typeof val === "object" && typeof val.id === "number" && typeof val.name === "string", { message });

export const createFormLinkSchema = z.object({
	form: requiredEntitySchema("Selecione um formulário."),
	cell: requiredEntitySchema("Selecione uma célula."),
	activity: requiredEntitySchema("Selecione uma atividade."),
	cellByComponent: entitySchema.optional(),
	cellByProductType: entitySchema.optional(),
});

export type Entity = z.infer<typeof entitySchema>;
export type TCreateFormLink = z.infer<typeof createFormLinkSchema>;
