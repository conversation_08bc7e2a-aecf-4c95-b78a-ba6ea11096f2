import { fieldsGroups<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/group.atom";
import { useCreateForm } from "@/modules/inspection/hooks/form/create/form.hook";
import { useUpdateFormMutation } from "@/modules/inspection/hooks/form/edit/mutation.hook";
import { useFormFindById } from "@/modules/inspection/hooks/form/list/find-by-id.hook";
import { InspectionFormFindByIdToFormMapper } from "@/modules/inspection/lib/mappers/forms/find-by-id-to-form.mapper";
import { InspectionFormToUpdateMapper } from "@/modules/inspection/lib/mappers/forms/form-to-update.mapper";
import { ICreateForm } from "@/modules/inspection/validators/form/create";
import { FormLoading } from "@/shared/components/custom/loading";
import { Modal } from "@/shared/components/custom/modal";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect } from "react";
import { FormCreateForm } from "../form-item/form";

interface IModalEditFormProps {
	isOpen: boolean;
	onClose: () => void;
	formId: string;
	canEdit: boolean;
}

export const ModalEditForm: React.FC<IModalEditFormProps> = ({ isOpen, onClose, formId, canEdit }) => {
	const { methods, ...formActions } = useCreateForm();
	const fields = useAtomValue(fieldsGroupsAtom);
	const { data, isLoading, hasError, error } = useFormFindById(formId, isOpen);
	const setFieldsGroups = useSetAtom(fieldsGroupsAtom);

	const handleClose = () => {
		setFieldsGroups([]);
		onClose();
	};

	const { updateForm } = useUpdateFormMutation(handleClose);

	useEffect(() => {
		if (!isOpen || !data) return;
		methods.reset(InspectionFormFindByIdToFormMapper.main(data));
		setFieldsGroups(InspectionFormFindByIdToFormMapper.fields(data));
	}, [data, methods, setFieldsGroups, isOpen]);

	const handleSubmit = (formData: ICreateForm) => updateForm({ form: InspectionFormToUpdateMapper.map(formData, fields), id: formId });

	return (
		<Modal
			isOpen={isOpen}
			onClose={handleClose}
			className="!h-[95vh] !w-full !max-w-[95vw] md:!h-auto md:!w-[1400px] md:!max-w-none"
			title={canEdit ? "Edição de Formulário" : "Visualização de Formulário"}
		>
			{isLoading && <FormLoading message="Carregando dados do formulário..." variant="full" />}
			{hasError && (
				<div className="flex items-center justify-center p-8">
					<div className="space-y-2 text-center">
						<p className="text-destructive font-medium">Erro ao carregar formulário</p>
						<p className="text-muted-foreground text-sm">{error}</p>
					</div>
				</div>
			)}
			{data && (
				<FormCreateForm mode={data.canUpdate ? "edit" : "view"} onClose={handleClose} methods={methods} onSubmit={handleSubmit} {...formActions} />
			)}
		</Modal>
	);
};
