import { AxiosInstance, AxiosResponse, AxiosRequestConfig as OriginalAxiosRequestConfig } from "axios";
import { AUTH_ENDPOINTS } from "../api/endpoints";
import { refreshRequest } from "../api/requests/refresh";
import { AUTH_TIMEOUTS } from "../constants/auth-timeouts";
import { getAuthToken } from "../lib/auth-actions";

interface AxiosRequestConfig extends OriginalAxiosRequestConfig {
	_retry?: boolean;
}

export const REFRESH_ERROR_MESSAGE = "O token de acesso expirou. Por favor, faça login novamente.";

class RefreshService {
	private isRefreshing = false;
	private refreshSubscribers: Array<(token: string) => void> = [];
	private refreshPromise: Promise<string> | null = null;
	private readonly nonRefreshableEndpoints = [AUTH_ENDPOINTS.LOGIN, AUTH_ENDPOINTS.REFRESH, AUTH_ENDPOINTS.LOGOUT];

	constructor(private readonly axiosInstance: AxiosInstance) {}

	public isNonRefreshableEndpoint(url: string): boolean {
		return this.nonRefreshableEndpoints.some(endpoint => url.includes(endpoint));
	}

	private resetRefreshState(): void {
		this.isRefreshing = false;
		this.refreshSubscribers = [];
		this.refreshPromise = null;
	}

	private notifySubscribers(token: string): void {
		this.refreshSubscribers.forEach(cb => cb(token));
		this.resetRefreshState();
	}

	private async performRefresh(): Promise<string> {
		try {
			const refreshResult = await refreshRequest();
			if (!refreshResult.success) throw new Error(REFRESH_ERROR_MESSAGE);
			const access_token = await getAuthToken();
			if (!access_token) throw new Error(REFRESH_ERROR_MESSAGE);
			return access_token;
		} catch (error) {
			console.error("Erro durante refresh do token:", error);
			throw error;
		}
	}

	public async handleTokenRefresh(originalRequest: AxiosRequestConfig): Promise<AxiosResponse> {
		if (this.isRefreshing && this.refreshPromise) {
			return new Promise((resolve, reject) => {
				const timeout = setTimeout(() => {
					reject(new Error("Timeout no refresh do token"));
				}, AUTH_TIMEOUTS.REFRESH_INTERCEPTOR);
				this.refreshSubscribers.push(token => {
					clearTimeout(timeout);
					if (token === REFRESH_ERROR_MESSAGE) {
						reject(new Error(REFRESH_ERROR_MESSAGE));
					} else {
						originalRequest.headers = { ...originalRequest.headers, Authorization: `Bearer ${token}` };
						resolve(this.axiosInstance(originalRequest));
					}
				});
			});
		}

		this.isRefreshing = true;
		originalRequest._retry = true;

		try {
			this.refreshPromise = this.performRefresh();
			const access_token = await this.refreshPromise;
			this.notifySubscribers(access_token);
			originalRequest.headers = { ...originalRequest.headers, Authorization: `Bearer ${access_token}` };
			return this.axiosInstance(originalRequest);
		} catch (error) {
			console.error("Erro no processo de refresh:", error);
			this.notifySubscribers(REFRESH_ERROR_MESSAGE);
			if (error instanceof Error) throw error;
			throw new Error(String(error));
		}
	}
}

export default RefreshService;
