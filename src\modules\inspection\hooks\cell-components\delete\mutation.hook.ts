import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../../core/toast";
import { createDeleteRequest } from "../../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";
import { CELL_COMPONENTS_ENDPOINTS } from "../../../api/endpoints";
import { inspectionKeys } from "../../../constants/query/keys";

export const useDeleteCellComponentMutation = () => {
	const queryClient = useQueryClient();

	const deleteCellMutation = useMutation({
		mutationKey: inspectionKeys.cellByComponents.custom("delete"),
		mutationFn: async (id: string) => {
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(CELL_COMPONENTS_ENDPOINTS.DELETE(id));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => inspectionKeys.cellByComponents.invalidateAll(queryClient),
	});

	return {
		deleteCellComponent: (id: string) =>
			toast.promise(deleteCellMutation.mutateAsync(id), {
				loading: "Deletando ...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
