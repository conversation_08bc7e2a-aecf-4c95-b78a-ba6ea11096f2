"use client";
import { reorderItemsInGroup<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/field-actions.atom";
import { reorderFieldGroupsAtom } from "@/modules/inspection/atoms/forms/fields/group-actions.atom";
import { fieldsGroupsAtom } from "@/modules/inspection/atoms/forms/fields/group.atom";
import { IFieldGroup } from "@/modules/inspection/types/forms/fields-table/fields-group.type";
import { IFlattenedFieldMobile } from "@/modules/inspection/types/forms/mobile/mobile-fields-cards.type";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { useAtomValue, useSetAtom } from "jotai";
import { useCallback, useMemo, useState } from "react";

export const useMobileFieldsReorder = () => {
	const fieldsGroups = useAtomValue(fieldsGroupsAtom);
	const reorderItems = useSetAtom(reorderItemsInGroupAtom);
	const reorderGroups = useSetAtom(reorderFieldGroupsAtom);
	const [isReordering, setIsReordering] = useState(false);
	const [reorderingField, setReorderingField] = useState<string | null>(null);
	const [reorderingGroup, setReorderingGroup] = useState<string | null>(null);

	const flattenedFields = useMemo((): IFlattenedFieldMobile[] => {
		return fieldsGroups.flatMap(group =>
			group.items.map((field: ICreateFieldForm, index: number) => ({
				...field,
				groupId: group.tempId,
				groupTitle: group.groupTitle,
				indexInGroup: index,
				totalInGroup: group.items.length,
			})),
		);
	}, [fieldsGroups]);

	const fieldLookup = useMemo(() => {
		const lookup = new Map<string, IFlattenedFieldMobile>();
		flattenedFields.forEach(field => {
			lookup.set(field.tempId, field);
		});
		return lookup;
	}, [flattenedFields]);

	const getFieldById = useCallback(
		(tempId: string): IFlattenedFieldMobile | undefined => {
			return fieldLookup.get(tempId);
		},
		[fieldLookup],
	);

	const handleMoveUp = useCallback(
		async (field: IFlattenedFieldMobile) => {
			if (field.indexInGroup === 0 || isReordering) return;

			setIsReordering(true);
			setReorderingField(field.tempId);

			try {
				await new Promise(resolve => setTimeout(resolve, 150));

				reorderItems({
					groupId: field.groupId,
					fromIndex: field.indexInGroup,
					toIndex: field.indexInGroup - 1,
				});
			} finally {
				setTimeout(() => {
					setIsReordering(false);
					setReorderingField(null);
				}, 100);
			}
		},
		[reorderItems, isReordering],
	);

	const handleMoveDown = useCallback(
		async (field: IFlattenedFieldMobile) => {
			if (field.indexInGroup === field.totalInGroup - 1 || isReordering) return;

			setIsReordering(true);
			setReorderingField(field.tempId);

			try {
				await new Promise(resolve => setTimeout(resolve, 150));

				reorderItems({
					groupId: field.groupId,
					fromIndex: field.indexInGroup,
					toIndex: field.indexInGroup + 1,
				});
			} finally {
				setTimeout(() => {
					setIsReordering(false);
					setReorderingField(null);
				}, 100);
			}
		},
		[reorderItems, isReordering],
	);

	const canMoveUp = useCallback(
		(field: IFlattenedFieldMobile): boolean => {
			return field.indexInGroup > 0 && !isReordering;
		},
		[isReordering],
	);

	const canMoveDown = useCallback(
		(field: IFlattenedFieldMobile): boolean => {
			return field.indexInGroup < field.totalInGroup - 1 && !isReordering;
		},
		[isReordering],
	);

	const isFieldReordering = useCallback(
		(fieldId: string): boolean => {
			return reorderingField === fieldId;
		},
		[reorderingField],
	);

	const isGroupReordering = useCallback(
		(groupId: string): boolean => {
			return reorderingGroup === groupId;
		},
		[reorderingGroup],
	);

	const handleMoveGroupUp = useCallback(
		async (group: IFieldGroup) => {
			const groupIndex = fieldsGroups.findIndex(g => g.tempId === group.tempId);
			if (groupIndex === 0 || isReordering) return;

			setIsReordering(true);
			setReorderingGroup(group.tempId);

			try {
				await new Promise(resolve => setTimeout(resolve, 150));

				reorderGroups({
					fromIndex: groupIndex,
					toIndex: groupIndex - 1,
				});
			} finally {
				setTimeout(() => {
					setIsReordering(false);
					setReorderingGroup(null);
				}, 100);
			}
		},
		[fieldsGroups, reorderGroups, isReordering],
	);

	const handleMoveGroupDown = useCallback(
		async (group: IFieldGroup) => {
			const groupIndex = fieldsGroups.findIndex(g => g.tempId === group.tempId);
			if (groupIndex === fieldsGroups.length - 1 || isReordering) return;
			setIsReordering(true);
			setReorderingGroup(group.tempId);

			try {
				await new Promise(resolve => setTimeout(resolve, 150));
				reorderGroups({
					fromIndex: groupIndex,
					toIndex: groupIndex + 1,
				});
			} finally {
				setTimeout(() => {
					setIsReordering(false);
					setReorderingGroup(null);
				}, 100);
			}
		},
		[fieldsGroups, reorderGroups, isReordering],
	);

	const canMoveGroupUp = useCallback(
		(group: IFieldGroup): boolean => {
			const groupIndex = fieldsGroups.findIndex(g => g.tempId === group.tempId);
			return groupIndex > 0 && !isReordering;
		},
		[fieldsGroups, isReordering],
	);

	const canMoveGroupDown = useCallback(
		(group: IFieldGroup): boolean => {
			const groupIndex = fieldsGroups.findIndex(g => g.tempId === group.tempId);
			return groupIndex < fieldsGroups.length - 1 && !isReordering;
		},
		[fieldsGroups, isReordering],
	);

	const getFieldsByGroup = useCallback(
		(groupId: string): IFlattenedFieldMobile[] => {
			return flattenedFields.filter(field => field.groupId === groupId);
		},
		[flattenedFields],
	);

	const filterFields = useCallback(
		(searchTerm: string): IFlattenedFieldMobile[] => {
			if (!searchTerm.trim()) return flattenedFields;

			const lowerSearchTerm = searchTerm.toLowerCase();
			return flattenedFields.filter(
				field =>
					field.field.name.toLowerCase().includes(lowerSearchTerm) ||
					field.nickname?.toLowerCase().includes(lowerSearchTerm) ||
					field.groupTitle?.toLowerCase().includes(lowerSearchTerm),
			);
		},
		[flattenedFields],
	);

	const performanceMetrics = useMemo(
		() => ({
			totalFields: flattenedFields.length,
			totalGroups: fieldsGroups.length,
			isLazyLoading: flattenedFields.length > 50,
			averageFieldsPerGroup: fieldsGroups.length > 0 ? Math.round(flattenedFields.length / fieldsGroups.length) : 0,
		}),
		[flattenedFields.length, fieldsGroups.length],
	);

	return {
		flattenedFields,
		fieldsGroups,
		performanceMetrics,
		isReordering,
		reorderingField,
		reorderingGroup,
		handleMoveUp,
		handleMoveDown,
		handleMoveGroupUp,
		handleMoveGroupDown,
		getFieldById,
		getFieldsByGroup,
		filterFields,
		canMoveUp,
		canMoveDown,
		canMoveGroupUp,
		canMoveGroupDown,
		isFieldReordering,
		isGroupReordering,
	};
};
