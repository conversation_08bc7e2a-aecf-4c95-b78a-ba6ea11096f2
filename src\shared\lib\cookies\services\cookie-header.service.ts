import { ICookieData, I<PERSON>ookieHeaderBuilder } from "@/shared/types/requests/cookie-header.type";

export class CookieHeaderService implements ICookieHeaderBuilder {
	private readonly excludedCookies = ["__next_hmr_refresh_hash__", "__next_hmr_refresh_hash", "__next_hmr_refresh_hash_"];

	private sanitizeCookieValue(value: string): string {
		const sanitized = value.replace(/[\r\n\t]/g, "").trim();
		if (/[^\x20-\x7E]/.test(sanitized) || /[";,\\\r\n]/.test(sanitized)) return encodeURIComponent(sanitized);
		return sanitized;
	}

	public buildCookieHeader(cookies: Record<string, string>): string {
		const validCookies = Object.entries(cookies)
			.filter(([name, value]) => this.shouldIncludeCookie(name, value))
			.map(([name, value]) => `${name}=${this.sanitizeCookieValue(value)}`);
		return validCookies.join("; ");
	}

	public shouldIncludeCookie(cookieName: string, cookieValue: string): boolean {
		if (!cookieValue || this.excludedCookies.includes(cookieName)) return false;
		if (cookieName.length === 0 || cookieValue.length === 0) return false;
		if (cookieValue.trim().length === 0) return false;
		return true;
	}

	public processCookieData(cookieData: ICookieData): string | null {
		if (!cookieData.success || !cookieData.value) return null;
		const cookieHeader = this.buildCookieHeader(cookieData.value);
		return cookieHeader || null;
	}
}
