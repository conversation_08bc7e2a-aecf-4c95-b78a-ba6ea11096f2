"use client";

import { useAtomValue } from "jotai";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { abilityAtom, userAtom, userNameAtom, userPermissionsAtom } from "../../atoms/user.atom";
import { IUser } from "../../types/user.types";

export const useUser = () => {
	const user = useAtomValue<IUser | null>(userAtom);
	const userName = useAtomValue(userNameAtom);
	const permissions = useAtomValue(userPermissionsAtom);
	const isAuthenticated = useAtomValue(isAuthenticatedAtom);
	const ability = useAtomValue(abilityAtom);

	return {
		user,
		userName,
		permissions,
		isAuthenticated,
		ability,
	};
};
