# Guia do useStreamingManager

O hook `useStreamingManager` foi aprimorado com novos métodos para controle completo de streaming de chat. Este guia documenta todas as funcionalidades disponíveis.

## Métodos Disponíveis

### Métodos Principais

#### `sendMessage(content: string): Promise<void>`
Envia uma nova mensagem e inicia o streaming da resposta.
- **Parâmetros**: `content` - O conteúdo da mensagem a ser enviada
- **Comportamento**: Se já houver um streaming ativo, ele será interrompido antes de iniciar o novo

#### `stop(): void`
Para/aborta o streaming atual.
- **Comportamento**: 
  - Aborta a requisição de streaming
  - Atualiza a mensagem atual com "Resposta interrompida pelo usuário"
  - Limpa o estado de streaming

#### `pause(): void`
Pausa o streaming atual (funcionalidade preparada para implementação futura).
- **Comportamento**: Marca o streaming como pausado, mas mantém a conexão ativa

#### `resume(): void`
Retoma um streaming pausado (funcionalidade preparada para implementação futura).
- **Comportamento**: Remove o estado de pausa e continua o streaming

#### `reset(): void`
Reseta completamente o estado de streaming.
- **Comportamento**: 
  - Aborta qualquer streaming ativo
  - Limpa todos os estados
  - Remove mensagens de erro

### Estado do Streaming

#### `isStreaming: boolean`
Indica se há um streaming ativo no momento.

#### `isPaused: boolean`
Indica se o streaming atual está pausado.

#### `currentMessageId: string | null`
ID da mensagem que está sendo transmitida no momento.

### Utilitários

#### `canSend: boolean`
Indica se é possível enviar uma nova mensagem.
- `true` quando não há streaming ativo e há uma sessão válida
- `false` durante streaming ou sem sessão

#### `canStop: boolean`
Indica se é possível parar o streaming atual.
- `true` quando há streaming ativo
- `false` quando não há streaming

#### `canPause: boolean`
Indica se é possível pausar o streaming atual.
- `true` quando há streaming ativo e não está pausado
- `false` caso contrário

#### `canResume: boolean`
Indica se é possível retomar o streaming.
- `true` quando há streaming ativo e está pausado
- `false` caso contrário

## Exemplos de Uso

### Uso Básico

```tsx
import { useStreamingManager } from '../hooks/streaming-manager.hook';

const ChatComponent = () => {
  const { sendMessage, stop, isStreaming, canSend, canStop } = useStreamingManager();

  const handleSend = async () => {
    if (canSend) {
      await sendMessage("Olá, como você pode me ajudar?");
    }
  };

  const handleStop = () => {
    if (canStop) {
      stop();
    }
  };

  return (
    <div>
      {canStop ? (
        <button onClick={handleStop}>Parar</button>
      ) : (
        <button onClick={handleSend} disabled={!canSend}>
          Enviar
        </button>
      )}
      {isStreaming && <div>Streaming em andamento...</div>}
    </div>
  );
};
```

### Integração com ChatInput

O componente `ChatInput` foi atualizado para usar automaticamente os novos métodos:

```tsx
<ChatInput 
  onSend={sendMessage} 
  onStop={stop} 
  isStreaming={isStreaming}
  disabled={false}
  placeholder="Digite sua mensagem..."
/>
```

### Controle Avançado

```tsx
const AdvancedChatControls = () => {
  const {
    sendMessage,
    stop,
    pause,
    resume,
    reset,
    isStreaming,
    isPaused,
    currentMessageId,
    canSend,
    canStop,
    canPause,
    canResume
  } = useStreamingManager();

  return (
    <div>
      <button onClick={() => sendMessage("Teste")} disabled={!canSend}>
        Enviar
      </button>
      <button onClick={stop} disabled={!canStop}>
        Parar
      </button>
      <button onClick={pause} disabled={!canPause}>
        Pausar
      </button>
      <button onClick={resume} disabled={!canResume}>
        Retomar
      </button>
      <button onClick={reset}>
        Reset
      </button>
      
      <div>
        Status: {isStreaming ? (isPaused ? 'Pausado' : 'Ativo') : 'Inativo'}
      </div>
    </div>
  );
};
```

## Gerenciamento de Estado

O hook utiliza átomos Jotai para gerenciar o estado de streaming:

- `streamingStateAtom`: Estado principal do streaming
- `isStreamingAtom`: Derivado - indica se está fazendo streaming
- `isPausedAtom`: Derivado - indica se está pausado
- `currentStreamingMessageIdAtom`: Derivado - ID da mensagem atual

## Tratamento de Erros

- Erros durante o streaming são automaticamente capturados
- A mensagem de erro é exibida na interface
- O estado de streaming é limpo automaticamente em caso de erro
- O método `reset()` pode ser usado para limpar erros manualmente

## Integração com Serviços

O hook integra-se com:
- `chatStreamService`: Para controle de streaming HTTP
- `sendStreamingMessage`: Para envio de mensagens
- Átomos de sessão: Para gerenciamento de mensagens e sessões

## Notas de Implementação

1. **Controle de AbortController**: O `chatStreamService` gerencia internamente o `AbortController`
2. **Estados Derivados**: Muitos estados são derivados do estado principal para evitar inconsistências
3. **Callbacks Otimizados**: Todos os callbacks usam `useCallback` para otimização de performance
4. **Limpeza Automática**: O estado é limpo automaticamente quando necessário

## Funcionalidades Futuras

- **Pause/Resume**: Implementação completa de pausa e retomada de streaming
- **Retry**: Tentativa automática de reconexão em caso de falha
- **Progress**: Indicadores de progresso mais detalhados
- **Queue**: Fila de mensagens para envio sequencial
