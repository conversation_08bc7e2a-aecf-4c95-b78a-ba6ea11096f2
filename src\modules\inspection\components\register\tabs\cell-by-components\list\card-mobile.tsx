import { Badge } from "@/shared/components/shadcn/badge";
import { Card, CardContent } from "@/shared/components/shadcn/card";
import { Separator } from "@/shared/components/shadcn/separator";
import { Component } from "lucide-react";
import { IInspectionCellComponent } from "../../../../../types/cell-components/dtos/find-all.dto";
import { CellComponentActions } from "./actions";

interface ICellComponentCardMobileProps {
	cellComponent: IInspectionCellComponent;
}

export const CellComponentCardMobile = ({ cellComponent }: ICellComponentCardMobileProps) => {
	return (
		<Card className="relative border bg-white shadow-sm hover:shadow-md">
			<CardContent>
				<div className="mb-3 flex items-start justify-between">
					<h3 className="mr-2 line-clamp-2 flex-1 text-base leading-tight font-semibold">
						{cellComponent.cellName}
						<Badge variant="secondary" className="ml-2 text-xs">
							<Component className="h-3 w-3" />
						</Badge>
					</h3>
					<div className="bg-primary h-1.5 w-1.5 rounded-full opacity-60" />
				</div>
				<div className="mb-4 space-y-2">
					<div className="flex items-center justify-between">
						<span className="text-xs text-gray-500">Célula</span>
						<Badge variant="outline" className="bg-gray-50 px-2 py-0.5 font-mono text-xs text-gray-700">
							{cellComponent.cellName}
						</Badge>
					</div>
					<div className="flex items-center justify-between">
						<span className="text-xs text-gray-500">Componente</span>
						<Badge variant="outline" className="bg-gray-50 px-2 py-0.5 font-mono text-xs text-gray-700">
							{cellComponent.componentName}
						</Badge>
					</div>
				</div>
				<Separator className="my-4" />
				<div className="flex gap-1.5">
					<div className="flex-1">
						<CellComponentActions cellId={String(cellComponent.id)} name={`${cellComponent.cellName} - ${cellComponent.componentName}`} />
					</div>
				</div>
			</CardContent>
		</Card>
	);
};
