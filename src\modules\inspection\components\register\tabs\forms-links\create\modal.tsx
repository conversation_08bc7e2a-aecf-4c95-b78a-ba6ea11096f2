import { Modal } from "../../../../../../../shared/components/custom/modal";
import { useCreateFormLinkForm } from "../../../../../hooks/form-links/create/form.hook";
import { useCreateFormLinkMutation } from "../../../../../hooks/form-links/create/mutation.hook";
import { InspectionFormsLinkToCreateMapper } from "../../../../../lib/mappers/forms-link/form-to-create.mapper";
import { TCreateFormLink } from "../../../../../validators/forms-links/create";
import { FormCreateFormLink } from "./form";

interface ModalCreateFormLinkProps {
	isOpen: boolean;
	onClose: () => void;
}

export const ModalCreateFormLink = ({ isOpen, onClose }: ModalCreateFormLinkProps) => {
	const methods = useCreateFormLinkForm();
	const handleClose = () => {
		onClose();
		methods.reset();
	};
	const { createFormLink } = useCreateFormLinkMutation(handleClose);
	const handleSubmit = async (data: TCreateFormLink) => await createFormLink(InspectionFormsLinkToCreateMapper.map(data));

	return (
		<Modal isOpen={isOpen} onClose={handleClose} title="Vínculo de Formulário">
			<FormCreateFormLink methods={methods} onSubmit={handleSubmit} onClose={handleClose} />
		</Modal>
	);
};
