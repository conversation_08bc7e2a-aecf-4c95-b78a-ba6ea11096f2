import { updateFieldMeasure<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/field-values.atom";
import useFindAllMeasures from "@/modules/inspection/hooks/measures/list/find-all.hook";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { GenericSearchSelect } from "@/shared/components/custom/generic-search-select";
import { Row } from "@tanstack/react-table";
import { useSetAtom } from "jotai";
import { FieldValidationWrapper } from "../fields-error-wrapper/field-validation-wrapper";

export const InspectionFormMeasureRow = ({ row, mode }: { row: Row<ICreateFieldForm>; mode?: "create" | "edit" | "view" }) => {
	const { tempId, measure } = row.original;
	const updateField = useSetAtom(updateFieldMeasureAtom);

	const handleChange = (selected: { id: number | string; name: string }) => {
		updateField({
			tempId,
			measure: {
				...selected,
				id: typeof selected.id === "string" ? Number(selected.id) : selected.id,
			},
		});
	};

	return (
		<FieldValidationWrapper tempId={tempId} showValidationIcon={false} showErrorMessage={false}>
			<GenericSearchSelect
				disabled={mode === "view"}
				value={{ id: measure.id ?? 0, name: measure.name ?? "" }}
				useDataHook={useFindAllMeasures}
				onChange={handleChange}
				placeholder="Selecione..."
				searchPlaceholder="Buscar medida..."
				loadingText="Carregando..."
				emptyText="Nenhuma medida encontrada."
				width="w-full"
			/>
		</FieldValidationWrapper>
	);
};
