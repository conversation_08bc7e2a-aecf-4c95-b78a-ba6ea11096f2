"use client";

import { getItem } from "@/shared/lib/utils/local-storage";
import { ColumnDef } from "@tanstack/react-table";
import { useEffect, useCallback, useState } from "react";

export interface DataTableProps<TData, TValue> {
	columns: ColumnDef<TData, TValue>[];
	data: TData[];
	tableId?: string;
}

export const useOrderColumns = <TData, TValue>({ tableId = "default-table", columns }: DataTableProps<TData, TValue>) => {
	const loadColumnOrderFromStorage = useCallback(() => {
		const storageKey = `table-column-order-${tableId}`;
		const savedOrder = getItem<string[]>(storageKey);
		if (savedOrder) {
			const currentColumnIds = columns.map(c => c.id!);
			const isValidOrder =
				currentColumnIds.every(id => savedOrder.includes(id)) && savedOrder.every((id: string) => currentColumnIds.includes(id));

			if (isValidOrder) {
				return savedOrder;
			}
		}
		return columns.map(c => c.id!);
	}, [columns, tableId]);

	const [columnOrder, setColumnOrder] = useState<string[]>(() => loadColumnOrderFromStorage());

	const saveColumnOrderToStorage = useCallback(
		(newOrder: string[]) => {
			try {
				const storageKey = `table-column-order-${tableId}`;
				localStorage.setItem(storageKey, JSON.stringify(newOrder));
			} catch (error) {
				console.warn("Erro ao salvar ordem das colunas no localStorage:", error);
			}
		},
		[tableId]
	);

	useEffect(() => {
		saveColumnOrderToStorage(columnOrder);
	}, [columnOrder, saveColumnOrderToStorage]);

	const resetColumnOrder = useCallback(() => {
		const defaultOrder = columns.map(c => c.id!);
		setColumnOrder(defaultOrder);
		try {
			const storageKey = `table-column-order-${tableId}`;
			localStorage.removeItem(storageKey);
		} catch (error) {
			console.warn("Erro ao remover ordem das colunas do localStorage:", error);
		}
	}, [columns, tableId]);

	return {
		columnOrder,
		setColumnOrder,
		resetColumnOrder,
	};
};
