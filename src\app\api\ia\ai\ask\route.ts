import { NextRequest } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();

		// Fazer a requisição para o backend mantendo o streaming
		const response = await fetch("http://192.168.155.83:10/ai/ask", {
			method: "POST",
			headers: {
				Accept: "*/*",
				Authorization: request.headers.get("Authorization") || "",
				// tem que enviar nos cookies
				Cookie: request.headers.get("Cookie") || "",
				"Content-Type": "application/json",
			},
			body: JSON.stringify(body),
		});

		if (!response.ok) {
			return new Response(JSON.stringify({ error: `Erro ao conectar com a API de IA ${response.statusText}` }), { status: response.status });
		}

		// Retornar o stream diretamente
		return new Response(response.body, {
			headers: {
				"Content-Type": "text/event-stream",
				"Cache-Control": "no-cache",
				Connection: "keep-alive",
				"Access-Control-Allow-Origin": "*",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization",
			},
		});
	} catch (error) {
		console.error("Erro na API route:", error);
		return new Response(JSON.stringify({ error: "Erro interno do servidor" }), { status: 500 });
	}
}
