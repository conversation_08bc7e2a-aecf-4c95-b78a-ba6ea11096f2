import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { createFormLinkSchema, TCreateFormLink } from "../../../validators/forms-links/create";

export const useCreateFormLinkForm = () => {
	return useForm<TCreateFormLink>({
		resolver: zodResolver(createFormLinkSchema),
		defaultValues: {
			activity: undefined,
			form: undefined,
			cell: undefined,
			cellByComponent: undefined,
			cellByProductType: undefined,
		},
	});
};
