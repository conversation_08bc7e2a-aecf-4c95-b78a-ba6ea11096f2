import { cn } from "@/shared/lib/shadcn/utils";

interface PulseLoadingProps {
	className?: string;
	message?: string;
	size?: "sm" | "md" | "lg";
}

const sizeMap = {
	sm: "h-8 w-8",
	md: "h-12 w-12",
	lg: "h-16 w-16",
};

export const PulseLoading: React.FC<PulseLoadingProps> = ({ className, message = "Carregando...", size = "md" }) => {
	return (
		<div className={cn("flex flex-col items-center justify-center space-y-4", className)}>
			<div className="relative">
				<div className={cn("border-primary/20 animate-pulse rounded-full border-4", sizeMap[size])} />
				<div
					className={cn(
						"border-primary absolute inset-2 animate-spin rounded-full border-2 border-r-transparent",
						size === "sm" ? "inset-1" : size === "lg" ? "inset-3" : "inset-2",
					)}
				/>
				<div
					className={cn(
						"bg-primary absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform animate-pulse rounded-full",
						size === "sm" ? "h-2 w-2" : size === "lg" ? "h-4 w-4" : "h-3 w-3",
					)}
				/>
			</div>

			{message && (
				<div className="space-y-1 text-center">
					<p className="text-foreground text-sm font-medium">{message}</p>
					<div className="flex items-center justify-center space-x-1">
						{Array.from({ length: 3 }).map((_, index) => (
							<div
								key={index}
								className="bg-primary h-1 w-1 animate-bounce rounded-full"
								style={{
									animationDelay: `${index * 0.2}s`,
									animationDuration: "1s",
								}}
							/>
						))}
					</div>
				</div>
			)}
		</div>
	);
};
