import { Badge } from "@/shared/components/shadcn/badge";
import { ColumnDef } from "@tanstack/react-table";
import { <PERSON> } from "lucide-react";
import { IInspectionFormDto } from "../../../../../types/forms/dtos/find-all.dto";
import { FormListActions } from "./actions";

export const inspectionFormColumns: ColumnDef<IInspectionFormDto>[] = [
	{
		accessorKey: "titulo",
		header: () => <div className="text-start font-semibold">Título</div>,
		cell: ({ row }) => (
			<div className="flex items-center gap-2 text-start">
				<span className="text-primary block max-w-[200px] truncate font-medium">{row.original.title}</span>
				{!row.original.canUpdate && (
					<div className="bg-primary/5 flex items-center gap-1 rounded-full px-2 py-0.5">
						<Link className="text-primary h-3 w-3" />
					</div>
				)}
			</div>
		),
	},
	{
		accessorKey: "nomenclatura",
		header: () => <div className="text-center font-semibold">Nomenclatura</div>,
		cell: ({ row }) => (
			<div className="flex justify-center">
				<Badge variant="outline" className="bg-muted-foreground/10 rounded px-3 py-1 text-xs">
					{row.original.nomenclature}
				</Badge>
			</div>
		),
	},
	{
		accessorKey: "revisao",
		header: () => <div className="text-center font-semibold">Revisão</div>,
		cell: ({ row }) => (
			<div className="text-center">
				<span className="text-muted-foreground text-sm font-medium">{row.original.revision}</span>
			</div>
		),
	},
	{
		id: "actions",
		header: () => <div className="pr-2 text-right font-semibold">Ações</div>,
		cell: ({ row }) => <FormListActions hasLink={!row.original.canUpdate} formId={row.original.id} title={row.original.title} />,
		enableSorting: false,
		enableHiding: false,
		size: 80,
	},
];
