import { ReactNode } from "react";

export interface IReactChildrenType {
	children: ReactNode;
}

export interface IHandleModalComponentProps {
	isOpen: boolean;
	onClose: () => void;
	title?: string;
	description?: string;
	children: React.ReactNode;
	showCloseButton?: boolean;
	closeOnOverlayClick?: boolean;
	size?: "sm" | "md" | "lg" | "xl";
	className?: string;
	overlayClassName?: string;
	disableEscapeKeyDown?: boolean;
	initialFocusRef?: React.RefObject<HTMLElement>;
}
