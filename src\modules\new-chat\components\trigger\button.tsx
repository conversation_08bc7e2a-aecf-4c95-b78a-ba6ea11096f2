import { AnimatePresence, motion } from "framer-motion";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { <PERSON><PERSON>, <PERSON> } from "lucide-react";
import { Button } from "../../../../shared/components/shadcn/button";
import { cn } from "../../../../shared/lib/shadcn/utils";
import { chatIsOpenAtom, toggleChatStateAtom } from "../../atoms/controls/trigger.atom";
import { isMessagesAvailableAtom } from "../../atoms/session/info.atom";

interface ChatTriggerButtonProps {
	className?: string;
	size?: "sm" | "md" | "lg";
	sizeClasses: {
		sm: string;
		md: string;
		lg: string;
	};
	iconSizeClasses: {
		sm: string;
		md: string;
		lg: string;
	};
}

export const ChatTriggerButton = ({ className, size = "md", sizeClasses, iconSizeClasses }: ChatTriggerButtonProps) => {
	const isOpen = useAtomValue(chatIsOpenAtom);
	const toggleChat = useSetAtom(toggleChatStateAtom);
	const hasMessages = useAtomValue(isMessagesAvailableAtom);

	return (
		<motion.div
			whileHover={{ scale: 1.1 }}
			whileTap={{ scale: 0.95 }}
			animate={{
				scale: isOpen ? 1.1 : 1,
				rotate: isOpen ? 180 : 0,
			}}
			transition={{
				type: "spring",
				stiffness: 300,
				damping: 20,
			}}
		>
			<Button
				onClick={toggleChat}
				className={cn(
					"group rounded-main relative transition-all duration-300 ease-out",
					"from-primary via-primary/90 to-primary/80 bg-gradient-to-br",
					"hover:from-primary/90 hover:via-primary hover:to-primary",
					"hover:shadow-primary/25 shadow-lg hover:shadow-xl",
					"border-2 border-white/20 dark:border-white/10",
					"backdrop-blur-sm",
					sizeClasses[size],
					className,
				)}
				size="icon"
				aria-label={isOpen ? "Fechar chat" : "Abrir chat"}
			>
				<div className="rounded-main pointer-events-none absolute inset-0 bg-gradient-to-t from-black/10 to-white/10" />

				<AnimatePresence mode="sync">
					{isOpen ? (
						<motion.div
							key="close"
							initial={{ opacity: 0, rotate: -90 }}
							animate={{ opacity: 1, rotate: 0 }}
							exit={{ opacity: 0, rotate: 90 }}
							transition={{ duration: 0.2 }}
						>
							<X className={cn(iconSizeClasses[size], "text-white drop-shadow-sm")} />
						</motion.div>
					) : (
						<motion.div
							key="message"
							initial={{ opacity: 0, rotate: 90 }}
							animate={{ opacity: 1, rotate: 0 }}
							exit={{ opacity: 0, rotate: -90 }}
							transition={{ duration: 0.2 }}
							className="relative"
						>
							<Bot className={cn(iconSizeClasses[size], "text-white drop-shadow-sm")} />
							<AnimatePresence>
								{hasMessages && (
									<motion.div
										initial={{ scale: 0, opacity: 0 }}
										animate={{ scale: 1, opacity: 1 }}
										exit={{ scale: 0, opacity: 0 }}
										transition={{
											type: "spring",
											stiffness: 500,
											damping: 30,
										}}
										className="absolute -top-1 -right-1 flex items-center justify-center"
									>
										<motion.div
											animate={{ scale: [1, 1.2, 1] }}
											transition={{
												repeat: Infinity,
												duration: 2,
												ease: "easeInOut",
											}}
											className={cn(
												"rounded-main h-3 w-3 border-2 border-white bg-gradient-to-br from-red-400 to-red-600 shadow-sm dark:border-slate-900",
											)}
										/>
									</motion.div>
								)}
							</AnimatePresence>
						</motion.div>
					)}
				</AnimatePresence>
			</Button>
		</motion.div>
	);
};
