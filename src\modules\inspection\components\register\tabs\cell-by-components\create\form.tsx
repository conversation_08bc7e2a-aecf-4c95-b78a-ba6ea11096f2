import { X } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { Button } from "../../../../../../../shared/components/shadcn/button";
import { Form } from "../../../../../../../shared/components/shadcn/form";
import { TCreateCellComponentForm } from "../../../../../validators/cell-components/create";

interface ICreateCellComponentForm {
	methods: UseFormReturn<TCreateCellComponentForm>;
	onSubmit: (data: TCreateCellComponentForm) => void;
	onClose: () => void;
}

export const CreateInspectionCellComponentForm = ({ methods, onSubmit, onClose }: ICreateCellComponentForm) => {
	return (
		<Form {...methods}>
			<form onSubmit={methods.handleSubmit(onSubmit)} className="flex flex-col gap-4"></form>
			<div className="flex justify-end gap-2 pt-4">
				<Button type="button" variant="outline" onClick={onClose}>
					<X className="mr-2" /> Cancelar
				</Button>
			</div>
		</Form>
	);
};
