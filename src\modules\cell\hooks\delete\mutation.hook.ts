import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../core/toast";
import { createDeleteRequest } from "../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { CELL_ENDPOINTS } from "../../api/endpoints";
import { cellQueryKeys } from "../../constants/query";

export const useDeleteCellMutation = () => {
	const queryClient = useQueryClient();

	const deleteMutation = useMutation({
		mutationKey: cellQueryKeys.custom("delete"),
		mutationFn: async (id: string) => {
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(CELL_ENDPOINTS.DELETE(id));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => cellQueryKeys.invalidateAll(queryClient),
	});

	return {
		deleteCell: (id: string) =>
			toast.promise(deleteMutation.mutateAsync(id), {
				loading: "Excluindo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
