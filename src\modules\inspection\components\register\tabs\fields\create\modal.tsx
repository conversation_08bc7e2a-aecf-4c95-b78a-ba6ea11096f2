import { useCreateFieldsMutation } from "@/modules/inspection/hooks/fields/create/mutation.hook";
import { useCreateFields } from "@/modules/inspection/hooks/fields/create/form.hook";
import { ICreateFields } from "@/modules/inspection/validators/fields/create";
import { Modal } from "@/shared/components/custom/modal";
import FormCreateFields from "./form";

interface IModalCreateFormFields {
	isOpen: boolean;
	onClose: () => void;
}

export default function ModalCreateFields({ isOpen, onClose }: IModalCreateFormFields) {
	const { methods } = useCreateFields();
	const { createFields } = useCreateFieldsMutation();

	function handleSubmit(data: ICreateFields) {
		const payload = {
			...data,
			name: data.name,
		};
		createFields(payload);
		onClose();
		methods.reset();
	}
	return (
		<Modal isOpen={isOpen} onClose={onClose} className="!w-[500px] !max-w-none" title="Cadastro de Campos">
			<FormCreateFields methods={methods} onSubmit={handleSubmit} onClose={onClose} />
		</Modal>
	);
}
