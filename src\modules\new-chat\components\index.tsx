"use client";

import { ChatSidebar } from "./sidebar/container";
import { ChatTrigger } from "./trigger";

interface ChatProps {
	showTrigger?: boolean;
	triggerPosition?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
	triggerSize?: "sm" | "md" | "lg";
}

export const Chat = ({ showTrigger = true, triggerPosition = "bottom-right", triggerSize = "md" }: ChatProps) => {
	return (
		<>
			{showTrigger && <ChatTrigger position={triggerPosition} size={triggerSize} />} <ChatSidebar />
		</>
	);
};
