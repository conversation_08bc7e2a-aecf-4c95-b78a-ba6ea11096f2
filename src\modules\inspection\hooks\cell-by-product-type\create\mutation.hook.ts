import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../../core/toast";
import { createPostRequest } from "../../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";
import { ApiResponse } from "../../../../../shared/types/requests/request.type";
import { CELL_BY_PRODUCT_TYPE_ENDPOINTS } from "../../../api/endpoints";
import { inspectionKeys } from "../../../constants/query/keys";
import { TCreateCellByProductTypeForm } from "../../../validators/cell-by-product-type/create.validator";

export const useCreateCellByProductTypeMutation = () => {
	const queryClient = useQueryClient();

	const createFormMutation = useMutation({
		mutationKey: inspectionKeys.cellByProductType.custom("create"),
		mutationFn: async (formData: TCreateCellByProductTypeForm) => {
			const res = await createPostRequest<ApiResponse<IMessageGlobalReturn>>(CELL_BY_PRODUCT_TYPE_ENDPOINTS.CREATE, formData);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => inspectionKeys.cellByProductType.invalidateAllLists(queryClient),
	});

	return {
		createCellByProductType: (formData: TCreateCellByProductTypeForm) =>
			toast.promise(createFormMutation.mutateAsync(formData), {
				loading: "Criando tipo de produto da célula...",
				success: data => data.data.message ?? "Tipo de produto da célula criado com sucesso!",
				error: error => error.message || "Erro ao criar tipo de produto da célula",
			}),
	};
};
