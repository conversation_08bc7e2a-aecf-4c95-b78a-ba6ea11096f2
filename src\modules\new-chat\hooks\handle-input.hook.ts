import { useCallback } from "react";

interface ChatInputState {
	value: string;
	onChange: (value: string) => void;
	onSend: (content: string) => Promise<void>;
	onStop?: () => void;
	disabled?: boolean;
}

export const useChatInput = ({ value, onChange, onSend, onStop, disabled }: ChatInputState) => {
	const canSend = !disabled && value.trim().length > 0;

	const handleSend = useCallback(async () => {
		if (!canSend) return;
		const msg = value;
		onChange("");
		try {
			await onSend(msg);
		} catch (err) {
			console.error("Error sending message:", err);
		}
	}, [canSend, onSend, value, onChange]);

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
			if (e.key === "Enter" && !e.shiftKey) {
				e.preventDefault();
				handleSend();
			}
		},
		[handleSend],
	);

	return {
		canSend,
		canStop: !!onStop,
		handleKeyDown,
		handleSend,
	};
};
