import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { createCellComponentSchema, TCreateCellComponentForm } from "../../../validators/cell-components/create";

export const useCreateCellComponentForm = () => {
	return useForm<TCreateCellComponentForm>({
		resolver: zodResolver(createCellComponentSchema),
		defaultValues: {
			cellProductionId: undefined,
			componentId: undefined,
		},
	});
};
