"use client";
import {
	add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	getField<PERSON><PERSON><PERSON><PERSON><PERSON>,
	removeF<PERSON><PERSON><PERSON><PERSON><PERSON>,
	updateF<PERSON>Bi<PERSON>ilter<PERSON><PERSON>,
	updateF<PERSON>Data<PERSON>tom,
	updateFieldMeasure<PERSON>tom,
	updateF<PERSON>Nickname<PERSON>tom,
	updateF<PERSON><PERSON><PERSON><PERSON><PERSON>,
	update<PERSON><PERSON>Required<PERSON>tom,
	update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/modules/inspection/atoms/forms/fields/field-values.atom";
import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import useFindAllFields from "@/modules/inspection/hooks/fields/list/find-all.hook";
import useFindAllMeasures from "@/modules/inspection/hooks/measures/list/find-all.hook";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { GenericSearchSelect } from "@/shared/components/custom/generic-search-select";
import { Button } from "@/shared/components/shadcn/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/components/shadcn/card";
import { Checkbox } from "@/shared/components/shadcn/checkbox";
import { Input } from "@/shared/components/shadcn/input";
import { Label } from "@/shared/components/shadcn/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { Separator } from "@/shared/components/shadcn/separator";
import { motion } from "framer-motion";
import { useAtomValue, useSetAtom } from "jotai";
import { ChevronDown, ChevronUp, Plus, Trash2, X } from "lucide-react";
import React, { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { removeItemFromGroupAtom } from "../../../../../../atoms/forms/fields/field-actions.atom";
import { FieldValidationWrapper } from "../form-fields/fields-error-wrapper/field-validation-wrapper";

interface IMobileFieldCardProps {
	field: ICreateFieldForm;
	isFirst: boolean;
	isLast: boolean;
	isSelected: boolean;
	isReordering?: boolean;
	onMoveUp: () => void;
	onMoveDown: () => void;
	onCardClick: () => void;
	isGroupEmpty: boolean;
	removeGroup?: () => void;
	isView: boolean;
}

export const MobileFieldCard: React.FC<IMobileFieldCardProps> = React.memo(
	({ field, isFirst, isLast, isSelected, isReordering = false, onMoveUp, onMoveDown, onCardClick, isGroupEmpty, removeGroup, isView }) => {
		const removeItem = useSetAtom(removeItemFromGroupAtom);
		const updateFieldData = useSetAtom(updateFieldDataAtom);
		const updateFieldNickname = useSetAtom(updateFieldNicknameAtom);
		const updateFieldType = useSetAtom(updateFieldTypeAtom);
		const updateFieldMeasure = useSetAtom(updateFieldMeasureAtom);
		const updateFieldRequired = useSetAtom(updateFieldRequiredAtom);
		const updateFieldBiFilter = useSetAtom(updateFieldBiFilterAtom);
		const addFieldOption = useSetAtom(addFieldOptionAtom);
		const fieldOptions = useAtomValue(getFieldOptionsAtom);
		const removeFieldOption = useSetAtom(removeFieldOptionAtom);
		const updateFieldOption = useSetAtom(updateFieldOptionAtom);

		const [localNickname, setLocalNickname] = useState(field.nickname);
		const [localOptionValues, setLocalOptionValues] = useState<Record<string, string>>({});
		const options = fieldOptions(field.tempId);

		const handleDelete = () => (isGroupEmpty ? removeGroup?.() : removeItem({ itemId: field.tempId }));
		const handleNicknameBlur = () => localNickname !== field.nickname && updateFieldNickname({ tempId: field.tempId, nickname: localNickname || "" });
		const handleAddOption = () => addFieldOption({ tempId: field.tempId, newOption: { option: "", tempId: uuidv4() } });
		const handleOptionChange = (optionTempId: string, value: string) => setLocalOptionValues(prev => ({ ...prev, [optionTempId]: value }));
		const handleOptionBlur = (option: { sequence?: number; option: string; tempId: string; id?: number }) => {
			const localValue = localOptionValues[option.tempId] ?? option.option;
			if (localValue !== option.option) {
				updateFieldOption({
					tempId: field.tempId,
					optionTempId: option.tempId,
					updatedOption: {
						...option,
						sequence: option.sequence ?? 1,
						option: localValue,
					},
				});
			}
		};
		const handleRemoveOption = (optionTempId: string) => removeFieldOption({ tempId: field.tempId, optionTempId });

		return (
			<motion.div
				layout
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0, scale: isReordering ? 0.98 : 1 }}
				exit={{ opacity: 0, y: -10 }}
				transition={{ duration: 0.15, ease: "easeOut" }}
				className="w-full"
			>
				<Card
					className={`transition-all duration-200 ${
						isSelected ? "ring-primary border-primary/30 shadow-md ring-2" : "hover:border-primary/20 hover:shadow-sm"
					}`}
					onClick={!isView ? onCardClick : undefined}
				>
					<CardHeader className="pb-3">
						<div className="items-cente\r flex justify-between">
							<CardTitle className="text-base font-medium">Campo #{field.sequence}</CardTitle>
							<div className="flex items-center gap-1">
								<Button
									size="sm"
									variant="ghost"
									onClick={e => {
										e.preventDefault();
										onMoveUp();
									}}
									disabled={isView || ((isFirst || isReordering) && !isGroupEmpty)}
									className="text-muted-foreground hover:text-primary h-7 w-7 p-0 disabled:opacity-30"
									title="Mover para cima"
								>
									<ChevronUp className="h-3 w-3" />
								</Button>
								<Button
									size="sm"
									variant="ghost"
									onClick={e => {
										e.preventDefault();
										onMoveDown();
									}}
									disabled={isView || ((isLast || isReordering) && !isGroupEmpty)}
									className="text-muted-foreground hover:text-primary h-7 w-7 p-0 disabled:opacity-30"
									title="Mover para baixo"
								>
									<ChevronDown className="h-3 w-3" />
								</Button>
								<Button
									size="sm"
									variant="ghost"
									onClick={e => {
										e.preventDefault();
										handleDelete();
									}}
									disabled={isView || isReordering}
									className="text-muted-foreground hover:text-destructive h-7 w-7 p-0 disabled:opacity-30"
									title="Remover campo"
								>
									<Trash2 className="h-3 w-3" />
								</Button>
							</div>
						</div>
					</CardHeader>

					<CardContent className="space-y-4">
						<FieldRow label="Campo">
							<FieldValidationWrapper tempId={field.tempId} showValidationIcon={true} showErrorMessage={true}>
								<GenericSearchSelect
									value={{ id: field.field.id ?? 0, name: field.field.name }}
									disabled={isView}
									onChange={selected =>
										updateFieldData({
											tempId: field.tempId,
											field: {
												...selected,
												id: typeof selected.id === "string" ? Number(selected.id) : selected.id,
											},
										})
									}
									useDataHook={useFindAllFields}
									placeholder="Selecione um campo..."
									searchPlaceholder="Buscar campo..."
									loadingText="Carregando campos..."
									emptyText="Nenhum campo encontrado."
									width="w-full"
								/>
							</FieldValidationWrapper>
						</FieldRow>
						<FieldRow label="Apelido">
							<Input
								placeholder="Digite um apelido"
								value={localNickname}
								onChange={e => setLocalNickname(e.target.value)}
								onBlur={handleNicknameBlur}
								disabled={isView}
								className="w-full"
							/>
						</FieldRow>
						<FieldRow label="Tipo">
							<FieldValidationWrapper tempId={field.tempId} showValidationIcon={true} showErrorMessage={true}>
								<Select
									disabled={isView}
									value={field.typeId?.toString()}
									onValueChange={value =>
										!isView && updateFieldType({ tempId: field.tempId, typeId: Number(value) as InspectionFormTypeEnum })
									}
								>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="Selecione o tipo..." />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value={InspectionFormTypeEnum.INTEGER.toString()}>Inteiro</SelectItem>
										<SelectItem value={InspectionFormTypeEnum.TEXT.toString()}>Texto</SelectItem>
										<SelectItem value={InspectionFormTypeEnum.DATE.toString()}>Data</SelectItem>
										<SelectItem value={InspectionFormTypeEnum.HOUR.toString()}>Hora</SelectItem>
										<SelectItem value={InspectionFormTypeEnum.SECONDS.toString()}>Segundos</SelectItem>
										<SelectItem value={InspectionFormTypeEnum.OPTIONS.toString()}>Opções</SelectItem>
										<SelectItem value={InspectionFormTypeEnum.BOOLEAN.toString()}>Booleano</SelectItem>
										<SelectItem value={InspectionFormTypeEnum.DECIMAL.toString()}>Decimal</SelectItem>
										<SelectItem value={InspectionFormTypeEnum.DECIMAL2DIGITS.toString()}>Decimal (2 dígitos)</SelectItem>
										<SelectItem value={InspectionFormTypeEnum.IMAGE.toString()}>Imagem</SelectItem>
									</SelectContent>
								</Select>
							</FieldValidationWrapper>
						</FieldRow>
						<FieldRow label="Medida">
							<FieldValidationWrapper tempId={field.tempId} showValidationIcon={true} showErrorMessage={true}>
								<GenericSearchSelect
									value={{ id: field.measure.id ?? 0, name: field.measure.name ?? "" }}
									disabled={isView}
									onChange={selected =>
										updateFieldMeasure({
											tempId: field.tempId,
											measure: {
												...selected,
												id: typeof selected.id === "string" ? Number(selected.id) : selected.id,
											},
										})
									}
									useDataHook={useFindAllMeasures}
									placeholder="Selecione uma medida..."
									searchPlaceholder="Buscar medida..."
									loadingText="Carregando medidas..."
									emptyText="Nenhuma medida encontrada."
									width="w-full"
								/>
							</FieldValidationWrapper>
						</FieldRow>
						<div className="flex items-center justify-between">
							<CheckboxRow
								id={`required-${field.tempId}`}
								checked={field.required === true}
								onCheckedChange={checked => updateFieldRequired({ tempId: field.tempId, required: !!checked })}
								label="Obrigatório"
								disabled={isView}
							/>
							<CheckboxRow
								id={`bi-filter-${field.tempId}`}
								checked={field.biFilter === true}
								onCheckedChange={checked => updateFieldBiFilter({ tempId: field.tempId, biFilter: !!checked })}
								label="Filtro BI"
								disabled={isView}
							/>
						</div>
						{field.typeId === InspectionFormTypeEnum.OPTIONS && (
							<>
								<Separator />
								<div className="space-y-3">
									<div className="flex items-center justify-between">
										<Label className="text-sm font-medium">Opções</Label>
										<Button type="button" variant="outline" size="sm" onClick={handleAddOption} disabled={isView}>
											<Plus className="mr-1 h-3 w-3" />
											Adicionar
										</Button>
									</div>
									{options.length === 0 ? (
										<p className="text-muted-foreground text-sm">Nenhuma opção adicionada.</p>
									) : (
										<div className="space-y-2">
											{options.map((option, index) => (
												<div key={option.tempId} className="flex items-center gap-2">
													<span className="text-muted-foreground w-6 text-xs">{index + 1}.</span>
													<Input
														placeholder="Digite uma opção"
														value={localOptionValues[option.tempId] ?? option.option}
														onChange={e => handleOptionChange(option.tempId, e.target.value)}
														onBlur={() => handleOptionBlur(option)}
														disabled={isView}
														className="flex-1"
													/>
													<Button
														type="button"
														variant="ghost"
														size="sm"
														onClick={() => handleRemoveOption(option.tempId)}
														disabled={isView}
														className="hover:bg-destructive/10 hover:text-destructive h-8 w-8 p-0"
														title="Remover opção"
													>
														<X className="h-3 w-3" />
													</Button>
												</div>
											))}
										</div>
									)}
								</div>
							</>
						)}
					</CardContent>
				</Card>
			</motion.div>
		);
	},
);

MobileFieldCard.displayName = "MobileFieldCard";

const FieldRow: React.FC<{ label: string; children: React.ReactNode }> = ({ label, children }) => (
	<div className="space-y-2">
		<Label className="text-sm font-medium">{label}</Label>
		{children}
	</div>
);

const CheckboxRow: React.FC<{
	id: string;
	checked: boolean;
	onCheckedChange: (checked: boolean) => void;
	label: string;
	disabled?: boolean;
}> = ({ id, checked, onCheckedChange, label, disabled }) => (
	<div className="flex items-center space-x-2">
		<Checkbox id={id} checked={checked} onCheckedChange={onCheckedChange} disabled={disabled} />
		<Label htmlFor={id} className="text-sm">
			{label}
		</Label>
	</div>
);
