import { pathService } from "@/config/path-manager/service";
import { ACTION_LIST, IDefineCaslPermission, ROLE_LIST, SUBJECT_LIST, TPermissionAction, TPermissionSubject, TRoleList } from "@/config/permissions";
import { IUser } from "@/core/auth/types/user.types";
import { decodeJWT } from "@/shared/lib/jwt/decode";
import { defineAbilitiesFor } from "@/shared/lib/permissions/ability";
import { IDecodedAccessToken } from "@/shared/types/cookies/decoded-access-token";
import { NextRequest, NextResponse } from "next/server";

function extractRoles(decodedToken: IDecodedAccessToken): TRoleList[] {
	if (
		typeof decodedToken.resource_access === "object" &&
		decodedToken.resource_access !== null &&
		"simp" in decodedToken.resource_access &&
		decodedToken.resource_access.simp &&
		typeof decodedToken.resource_access.simp === "object" &&
		Array.isArray((decodedToken.resource_access.simp as { roles?: unknown }).roles)
	) {
		return ((decodedToken.resource_access.simp as { roles: string[] }).roles || [])
			.filter((r): r is TRoleList => Object.values(ROLE_LIST).includes(r as TRoleList))
			.map(r => r as TRoleList);
	}
	return [];
}

function extractPermissions(decodedToken: IDecodedAccessToken): IDefineCaslPermission[] {
	type PermissionPayload = { scopes?: string[]; rsname?: string };
	const validActions: TPermissionAction[] = Object.values(ACTION_LIST);
	const validSubjects: TPermissionSubject[] = Object.values(SUBJECT_LIST);
	if (
		typeof decodedToken.authorization === "object" &&
		decodedToken.authorization !== null &&
		Array.isArray((decodedToken.authorization as { permissions?: unknown }).permissions)
	) {
		return (decodedToken.authorization as { permissions: PermissionPayload[] }).permissions.flatMap(p => {
			const actions = p.scopes && p.scopes.length > 0 ? p.scopes : ["manage"];
			return actions
				.filter((action): action is TPermissionAction => validActions.includes(action as TPermissionAction))
				.map(action => ({
					action: action as TPermissionAction,
					subject: p.rsname && validSubjects.includes(p.rsname as TPermissionSubject) ? (p.rsname as TPermissionSubject) : "all",
				}));
		});
	}
	return [];
}

export const permissionsMiddleware = (request: NextRequest): NextResponse => {
	try {
		const { pathname } = request.nextUrl;
		if (pathname.includes(".") && !pathname.startsWith("/api/") && !pathname.startsWith("/auth/")) return NextResponse.next();
		const accessToken = request.cookies.get("access_token")?.value;
		if (!accessToken) {
			console.warn("[permissionsMiddleware] Token de acesso não encontrado.");
			return NextResponse.rewrite(new URL("/login", request.url));
		}
		const decodedToken = decodeJWT<IDecodedAccessToken>(accessToken);
		if (!decodedToken || typeof decodedToken !== "object") {
			console.warn("[permissionsMiddleware] Token inválido ou não decodificado corretamente.");
			return NextResponse.rewrite(new URL("/login", request.url));
		}
		const user: IUser = {
			id: decodedToken.sub ? String(decodedToken.sub) : "",
			name: decodedToken.name ? String(decodedToken.name) : "",
			email: decodedToken.email ? String(decodedToken.email) : "",
			roles: extractRoles(decodedToken),
			permissions: extractPermissions(decodedToken),
		};
		const ability = defineAbilitiesFor(user);
		const currentRoute = pathService.getItemByPath(pathname);
		if (currentRoute && !currentRoute.route.active) {
			console.warn(`[permissionsMiddleware] Rota inativa: ${pathname}`);
			return NextResponse.rewrite(new URL("/404", request.url));
		}
		const hasPermission = currentRoute ? pathService.hasPermission(currentRoute, ability) : false;
		if (currentRoute && !hasPermission) {
			console.warn(`[permissionsMiddleware] Usuário sem permissão para acessar: ${pathname}`);
			return NextResponse.rewrite(new URL("/forbidden", request.url));
		}
		return NextResponse.next();
	} catch (error) {
		console.error("[permissionsMiddleware] Erro inesperado:", error);
		return NextResponse.rewrite(new URL("/login", request.url));
	}
};
