import { updateField<PERSON>i<PERSON><PERSON>er<PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields/field-values.atom";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { Checkbox } from "@/shared/components/shadcn/checkbox";
import { Row } from "@tanstack/react-table";
import { useSet<PERSON>tom } from "jotai";

export const InspectionFormBiFilterRow = ({ row, mode }: { row: Row<ICreateFieldForm>; mode?: "create" | "edit" | "view" }) => {
	const { tempId, biFilter } = row.original;
	const updateField = useSetAtom(updateFieldBiFilterAtom);

	return (
		<div className="flex w-full items-center justify-center">
			<Checkbox
				disabled={mode === "view"}
				className="bg-white"
				checked={biFilter === true}
				onCheckedChange={checked => updateField({ tempId, biFilter: !!checked })}
			/>
		</div>
	);
};
