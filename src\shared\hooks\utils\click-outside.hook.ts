import { useCallback, useEffect, useRef } from "react";

interface IUseClickOutsideOptions {
	enabled?: boolean;
	eventType?: "mousedown" | "mouseup" | "click";
}

export const useClickOutside = <T extends HTMLElement = HTMLElement>(callback: () => void, options: IUseClickOutsideOptions = {}) => {
	const { enabled = true, eventType = "mousedown" } = options;
	const ref = useRef<T>(null);

	const handleClickOutside = useCallback(
		(event: Event) => {
			if (!enabled) return;

			const target = event.target as Node;
			if (target instanceof Element) {
				const isSelectElement =
					target.closest('[data-slot="select-content"]') ||
					target.closest('[data-slot="select-value"]') ||
					target.closest('[data-slot="select-group"]') ||
					target.closest('[data-slot="select"]') ||
					target.closest('[data-slot="select-trigger"]') ||
					target.closest("[data-radix-select-content]") ||
					target.closest("[data-radix-select-trigger]") ||
					target.closest('[role="listbox"]') ||
					target.closest(".radix-select-content") ||
					target.closest(".shadcn-select-trigger") ||
					(target.hasAttribute("data-slot") &&
						(target.getAttribute("data-slot") === "select-trigger" || target.getAttribute("data-slot") === "select-content")) ||
					target.hasAttribute("data-radix-select-trigger") ||
					target.hasAttribute("data-radix-select-content");

				if (isSelectElement) {
					return;
				}

				const isButton = target.closest("button");
				if (
					isButton &&
					(isButton.hasAttribute("data-slot") ||
						isButton.hasAttribute("data-radix-select-trigger") ||
						isButton.classList.contains("shadcn-select-trigger"))
				) {
					return;
				}
			}

			if (ref.current && !ref.current.contains(target)) {
				callback();
			}
		},
		[callback, enabled],
	);
	useEffect(() => {
		if (!enabled) return;
		document.addEventListener(eventType, handleClickOutside);
		return () => {
			document.removeEventListener(eventType, handleClickOutside);
		};
	}, [handleClickOutside, eventType, enabled]);

	return ref;
};
