"use client";

import { IChatMessage } from "../../types/messages.type";
import { IChatSession } from "../../types/session.type";

class SessionService {
	private readonly STORAGE_KEY = "chat-session";

	private get isBrowser() {
		return typeof window !== "undefined";
	}

	private getStoredSession(): IChatSession | null {
		if (!this.isBrowser) return null;
		try {
			const stored = localStorage.getItem(this.STORAGE_KEY);
			return stored ? JSON.parse(stored) : null;
		} catch {
			return null;
		}
	}

	private saveSession(session: IChatSession): void {
		if (this.isBrowser) {
			localStorage.setItem(this.STORAGE_KEY, JSON.stringify(session));
		}
	}

	getSession(): IChatSession | null {
		return this.getStoredSession();
	}

	createSession(): IChatSession {
		const session: IChatSession = {
			id: `session_${Date.now()}`,
			messages: [],
			createdAt: new Date(),
			updatedAt: new Date(),
		};
		this.saveSession(session);
		return session;
	}

	clearSession(): void {
		if (this.isBrowser) {
			localStorage.removeItem(this.STORAGE_KEY);
		}
	}

	addMessage(message: IChatMessage): IChatSession {
		const session = this.getSession() || this.createSession();
		const updated = {
			...session,
			messages: [...session.messages, message],
			updatedAt: new Date(),
		};
		this.saveSession(updated);
		return updated;
	}

	updateMessage(messageId: string, updates: Partial<IChatMessage>): IChatSession | null {
		const session = this.getSession();
		if (!session) return null;
		const messages = session.messages.map(m => (m.id === messageId ? { ...m, ...updates } : m));
		const updated = { ...session, messages, updatedAt: new Date() };
		this.saveSession(updated);
		return updated;
	}
}

export const sessionService = new SessionService();
