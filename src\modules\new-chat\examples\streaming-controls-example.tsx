import React from "react";
import { Button } from "../../../shared/components/shadcn/button";
import { useStreamingManager } from "../hooks/streaming-manager.hook";

/**
 * Exemplo de como usar os novos métodos do useStreamingManager
 * Este componente demonstra todos os métodos disponíveis:
 * - sendMessage: Enviar uma nova mensagem
 * - stop: Parar o streaming atual
 * - pause: Pausar o streaming (funcionalidade futura)
 * - resume: Retomar o streaming pausado (funcionalidade futura)
 * - reset: Resetar o estado de streaming
 */
export const StreamingControlsExample = () => {
	const {
		// Métodos principais
		sendMessage,
		stop,
		pause,
		resume,
		reset,
		
		// Estado
		isStreaming,
		isPaused,
		currentMessageId,
		
		// Utilitários
		canSend,
		canStop,
		canPause,
		canResume,
	} = useStreamingManager();

	const handleSendExample = async () => {
		await sendMessage("Olá! Como você pode me ajudar hoje?");
	};

	const handleStop = () => {
		stop();
		console.log("Streaming interrompido");
	};

	const handlePause = () => {
		pause();
		console.log("Streaming pausado");
	};

	const handleResume = () => {
		resume();
		console.log("Streaming retomado");
	};

	const handleReset = () => {
		reset();
		console.log("Estado de streaming resetado");
	};

	return (
		<div className="p-6 space-y-4 border rounded-lg">
			<h3 className="text-lg font-semibold">Controles de Streaming</h3>
			
			{/* Estado atual */}
			<div className="p-4 bg-gray-100 rounded">
				<h4 className="font-medium mb-2">Estado Atual:</h4>
				<ul className="space-y-1 text-sm">
					<li>Streaming ativo: {isStreaming ? "Sim" : "Não"}</li>
					<li>Pausado: {isPaused ? "Sim" : "Não"}</li>
					<li>ID da mensagem atual: {currentMessageId || "Nenhuma"}</li>
				</ul>
			</div>

			{/* Controles principais */}
			<div className="space-y-2">
				<h4 className="font-medium">Controles Principais:</h4>
				<div className="flex gap-2 flex-wrap">
					<Button
						onClick={handleSendExample}
						disabled={!canSend}
						variant="default"
					>
						Enviar Mensagem de Exemplo
					</Button>
					
					<Button
						onClick={handleStop}
						disabled={!canStop}
						variant="destructive"
					>
						Parar Streaming
					</Button>
				</div>
			</div>

			{/* Controles avançados (funcionalidade futura) */}
			<div className="space-y-2">
				<h4 className="font-medium">Controles Avançados (Futuro):</h4>
				<div className="flex gap-2 flex-wrap">
					<Button
						onClick={handlePause}
						disabled={!canPause}
						variant="outline"
					>
						Pausar
					</Button>
					
					<Button
						onClick={handleResume}
						disabled={!canResume}
						variant="outline"
					>
						Retomar
					</Button>
					
					<Button
						onClick={handleReset}
						variant="outline"
					>
						Reset
					</Button>
				</div>
			</div>

			{/* Indicadores visuais */}
			<div className="space-y-2">
				<h4 className="font-medium">Indicadores:</h4>
				<div className="flex gap-2 flex-wrap">
					<span className={`px-2 py-1 rounded text-xs ${canSend ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}>
						Pode Enviar: {canSend ? "Sim" : "Não"}
					</span>
					<span className={`px-2 py-1 rounded text-xs ${canStop ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-600'}`}>
						Pode Parar: {canStop ? "Sim" : "Não"}
					</span>
					<span className={`px-2 py-1 rounded text-xs ${canPause ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-600'}`}>
						Pode Pausar: {canPause ? "Sim" : "Não"}
					</span>
					<span className={`px-2 py-1 rounded text-xs ${canResume ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600'}`}>
						Pode Retomar: {canResume ? "Sim" : "Não"}
					</span>
				</div>
			</div>
		</div>
	);
};

/**
 * Exemplo de uso em um componente personalizado
 */
export const CustomStreamingComponent = () => {
	const { sendMessage, stop, isStreaming, canSend, canStop } = useStreamingManager();
	const [inputValue, setInputValue] = React.useState("");

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (inputValue.trim() && canSend) {
			await sendMessage(inputValue.trim());
			setInputValue("");
		}
	};

	return (
		<div className="p-4 border rounded-lg">
			<h3 className="text-lg font-semibold mb-4">Chat Personalizado</h3>
			
			<form onSubmit={handleSubmit} className="space-y-4">
				<div>
					<input
						type="text"
						value={inputValue}
						onChange={(e) => setInputValue(e.target.value)}
						placeholder="Digite sua mensagem..."
						disabled={isStreaming}
						className="w-full p-2 border rounded"
					/>
				</div>
				
				<div className="flex gap-2">
					{canStop ? (
						<Button type="button" onClick={stop} variant="destructive">
							Parar
						</Button>
					) : (
						<Button type="submit" disabled={!canSend}>
							Enviar
						</Button>
					)}
				</div>
			</form>
			
			{isStreaming && (
				<div className="mt-4 p-2 bg-blue-100 text-blue-800 rounded">
					Streaming em andamento...
				</div>
			)}
		</div>
	);
};
