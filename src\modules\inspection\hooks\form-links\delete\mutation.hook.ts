import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../../core/toast";
import { createDeleteRequest } from "../../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../../shared/types/requests/message.type";
import { INSPECTION_FORMS_LINKS_ENDPOINTS } from "../../../api/endpoints";
import { inspectionKeys } from "../../../constants/query/keys";

export const useDeleteFormLinkMutation = () => {
	const queryClient = useQueryClient();

	const deleteFormLinkMutation = useMutation({
		mutationKey: inspectionKeys.formsLink.custom("delete"),
		mutationFn: async (id: string) => {
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(INSPECTION_FORMS_LINKS_ENDPOINTS.DELETE(id));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => inspectionKeys.formsLink.invalidateAll(queryClient),
	});

	return {
		deleteFormLink: (id: string) =>
			toast.promise(deleteFormLinkMutation.mutateAsync(id), {
				loading: "Excluíndo vínculo...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
