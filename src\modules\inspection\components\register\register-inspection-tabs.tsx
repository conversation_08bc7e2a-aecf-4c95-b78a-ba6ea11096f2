"use client";
import { Tabs, TabsContent } from "@/shared/components/shadcn/tabs";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Circle, Component, FileText, Grid, Link, Ruler, Users } from "lucide-react";
import { useState } from "react";
import { TInspectionTabValue, useInspectionTabs } from "../../hooks/tabs/inspection-tabs.hook";
import { ITabItemRegisterInspectionTabs, RegisterInspectionHeader } from "./header/register-inspection-header";
import { ModalCreateCellComponent } from "./tabs/cell-by-components/create/modal";
import { CellComponentsTable } from "./tabs/cell-by-components/list/table";
import { CellByProductTypeTable } from "./tabs/cell-by-product-type/list/table";
import ModalCreateFields from "./tabs/fields/create/modal";

import { ModalCreateForm } from "./tabs/forms/create/modal";
import { FormTable } from "./tabs/forms/list/table";
import ModalCreateMeasures from "./tabs/measures/create/modal";

import ModalCreateCollabSector from "./tabs/collaborator-by-sector/create/modal";
import { CollabBySectorTable } from "./tabs/collaborator-by-sector/list/table";
import { FieldsTable } from "./tabs/fields/list/table";
import { ModalCreateFormLink } from "./tabs/forms-links/create/modal";
import { FormsLinksTable } from "./tabs/forms-links/list/table";
import { MeasuresTable } from "./tabs/measures/list/table";

export const RegisterInspectionTabs = () => {
	const { activeTab, setActiveTab, availableTabs } = useInspectionTabs();
	const [searchTerm, setSearchTerm] = useState("");
	const inspectionModals = {
		measures: useModal(),
		fields: useModal(),
		forms: useModal(),
		components: useModal(),
		collaborators: useModal(),
		formLinks: useModal(),
	};

	const TAB_CONFIG: ITabItemRegisterInspectionTabs[] = [
		{
			value: "medidas",
			label: "Medidas",
			icon: Ruler,
			renderContent: searchTerm => <MeasuresTable searchTerm={searchTerm} />,
			onNew: () => inspectionModals.measures.openModal(),
		},
		{
			value: "campos",
			label: "Campos",
			icon: Grid,
			renderContent: searchTerm => <FieldsTable searchTerm={searchTerm} />,
			onNew: () => inspectionModals.fields.openModal(),
		},
		{
			value: "vinculo-colaboradores",
			label: "Vínculo de colaboradores",
			icon: Users,
			renderContent: searchTerm => <CollabBySectorTable searchTerm={searchTerm} />,
			onNew: () => inspectionModals.collaborators.openModal(),
		},
		{
			value: "formularios",
			label: "Formulários",
			icon: FileText,
			renderContent: searchTerm => <FormTable searchTerm={searchTerm} />,
			onNew: () => inspectionModals.forms.openModal(),
		},
		{
			value: "vinculos",
			label: "Vínculos",
			icon: Link,
			renderContent: searchTerm => <FormsLinksTable searchTerm={searchTerm} />,
			onNew: () => inspectionModals.formLinks.openModal(),
		},
		{
			value: "tipo-produto",
			label: "Tipo produto",
			icon: Circle,
			renderContent: searchTerm => <CellByProductTypeTable searchTerm={searchTerm} />,
			onNew: searchTerm => console.log("Novo em Células", searchTerm),
		},
		{
			value: "componentes",
			label: "Componentes",
			icon: Component,
			renderContent: searchTerm => <CellComponentsTable searchTerm={searchTerm} />,
			onNew: () => inspectionModals.components.openModal(),
		},
	];

	const handleTabChange = (value: string) => {
		if (availableTabs.includes(value as TInspectionTabValue)) setActiveTab(value as TInspectionTabValue);
	};

	const activeTabItem = TAB_CONFIG.find(tab => tab.value === activeTab);

	return (
		<div className="flex h-full w-full flex-1 flex-col gap-4">
			<Tabs value={activeTab} onValueChange={handleTabChange} className="relative flex h-full w-full flex-col">
				<section id="tabs-header" className="h-auto w-full">
					<RegisterInspectionHeader
						tabItems={TAB_CONFIG}
						activeTab={activeTab}
						setActiveTab={setActiveTab}
						searchTerm={searchTerm}
						setSearchTerm={setSearchTerm}
						onNew={searchTerm => activeTabItem?.onNew(searchTerm)}
					/>
				</section>
				<section id="tabs-content" className="flex-1">
					{TAB_CONFIG.map(({ value, renderContent }) => (
						<TabsContent key={value} value={value} className="mt-0">
							{renderContent(searchTerm)}
						</TabsContent>
					))}
				</section>
			</Tabs>
			<ModalCreateForm isOpen={inspectionModals.forms.isOpen} onClose={inspectionModals.forms.closeModal} />
			<ModalCreateMeasures isOpen={inspectionModals.measures.isOpen} onClose={inspectionModals.measures.closeModal} />
			<ModalCreateFields isOpen={inspectionModals.fields.isOpen} onClose={inspectionModals.fields.closeModal} />
			<ModalCreateCellComponent isOpen={inspectionModals.components.isOpen} onClose={inspectionModals.components.closeModal} />
			<ModalCreateCollabSector isOpen={inspectionModals.collaborators.isOpen} onClose={inspectionModals.collaborators.closeModal} />
			<ModalCreateFormLink isOpen={inspectionModals.formLinks.isOpen} onClose={inspectionModals.formLinks.closeModal} />
		</div>
	);
};
