import { cn } from "../../../lib/shadcn/utils";

interface ActionButton {
	label: string;
	onClick: () => void;
	variant?: "primary" | "secondary" | "outline";
	icon?: React.ReactNode;
}

interface EmptyStateProps {
	searchTerm?: string;
	icon: React.ReactNode;
	title: string;
	description: string;
	tip?: string;
	actions?: ActionButton[];
	size?: "sm" | "md" | "lg";
	variant?: "default" | "search" | "error" | "info";
	className?: string;
	children?: React.ReactNode;
	showDefaultSearchTip?: boolean;
}

const sizeClasses = {
	sm: {
		container: "px-4 py-6",
		iconContainer: "p-2 mb-3",
		title: "text-base",
		description: "text-xs",
		tip: "text-xs px-2 py-1",
	},
	md: {
		container: "px-6 py-8",
		iconContainer: "p-3 mb-4",
		title: "text-lg",
		description: "text-sm",
		tip: "text-xs px-3 py-2",
	},
	lg: {
		container: "px-8 py-12",
		iconContainer: "p-4 mb-6",
		title: "text-xl",
		description: "text-base",
		tip: "text-sm px-4 py-3",
	},
};

const variantClasses = {
	default: {
		iconBg: "bg-muted/50",
		tipBg: "bg-muted/30",
	},
	search: {
		iconBg: "bg-blue-50 dark:bg-blue-950/50",
		tipBg: "bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800",
	},
	error: {
		iconBg: "bg-red-50 dark:bg-red-950/50",
		tipBg: "bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800",
	},
	info: {
		iconBg: "bg-amber-50 dark:bg-amber-950/50",
		tipBg: "bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800",
	},
};

const buttonVariants = {
	primary: "bg-primary text-primary-foreground hover:bg-primary/90",
	secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
	outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
};

export const EmptyStateTable: React.FC<EmptyStateProps> = ({
	searchTerm,
	icon,
	title,
	description,
	tip,
	actions = [],
	size = "md",
	variant = "default",
	className,
	children,
	showDefaultSearchTip = true,
}) => {
	const isSearching = searchTerm && searchTerm.trim().length > 0;
	const shouldShowSearchTip = isSearching && showDefaultSearchTip;
	const defaultSearchTip = "💡 Dica: Verifique a grafia ou tente termos mais gerais";

	const currentVariant = isSearching && variant === "default" ? "search" : variant;
	const sizeConfig = sizeClasses[size];
	const variantConfig = variantClasses[currentVariant];

	const displayTip = tip || (shouldShowSearchTip ? defaultSearchTip : null);

	return (
		<div className={cn("flex flex-col items-center justify-center text-center", sizeConfig.container, className)}>
			<div className={cn("rounded-full transition-colors duration-200", sizeConfig.iconContainer, variantConfig.iconBg)}>{icon}</div>
			<h3 className={cn("text-foreground mb-2 font-semibold tracking-tight", sizeConfig.title)}>{title}</h3>
			<p className={cn("text-muted-foreground max-w-md leading-relaxed", sizeConfig.description)}>{description}</p>
			{isSearching && (
				<div className="text-muted-foreground mt-2 text-xs">
					Resultado da busca por: <span className="font-medium">{`"${searchTerm}"`}</span>
				</div>
			)}
			{children && <div className="mt-4">{children}</div>}
			{actions.length > 0 && (
				<div className="mt-6 flex flex-wrap justify-center gap-2">
					{actions.map((action, index) => (
						<button
							key={index}
							onClick={action.onClick}
							className={cn(
								"focus:ring-ring inline-flex items-center gap-2 rounded-md px-4 py-2 text-sm font-medium transition-colors duration-200 focus:ring-2 focus:ring-offset-2 focus:outline-none",
								buttonVariants[action.variant || "outline"],
							)}
						>
							{action.icon}
							{action.label}
						</button>
					))}
				</div>
			)}

			{displayTip && (
				<div className={cn("mt-4 rounded-md transition-colors duration-200", sizeConfig.tip, variantConfig.tipBg, "text-muted-foreground")}>
					{displayTip}
				</div>
			)}
		</div>
	);
};
