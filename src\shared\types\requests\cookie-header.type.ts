export interface ICookieHeaderConfig {
	headers?: {
		Cookie?: string;
		Authorization?: string;
		[key: string]: string | undefined;
	};
}

export interface ICookieData {
	success: boolean;
	message: string;
	value: Record<string, string> | null;
}

export interface ICookieHeaderBuilder {
	buildCookieHeader(cookies: Record<string, string>): string;
	shouldIncludeCookie(cookieName: string, cookieValue: string): boolean;
}
