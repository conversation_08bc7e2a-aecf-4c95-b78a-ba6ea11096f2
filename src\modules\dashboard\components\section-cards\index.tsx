import { Badge } from "@/shared/components/shadcn/badge";
import { Card, CardAction, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/shared/components/shadcn/card";
import { TrendingDown, TrendingUp } from "lucide-react";

export const SectionCards = () => {
	return (
		<div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
			<Card className="@container/card">
				<CardHeader>
					<CardDescription>Receita Total</CardDescription>
					<CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">R$ 1.250,00</CardTitle>
					<CardAction>
						<Badge variant="outline">
							<TrendingUp />
							+12,5%
						</Badge>
					</CardAction>
				</CardHeader>
				<CardFooter className="flex-col items-start gap-1.5 text-sm">
					<div className="line-clamp-1 flex gap-2 font-medium">
						Em alta este mês <TrendingUp className="size-4" />
					</div>
					<div className="text-muted-foreground">Visitantes dos últimos 6 meses</div>
				</CardFooter>
			</Card>
			<Card className="@container/card">
				<CardHeader>
					<CardDescription>Novos Clientes</CardDescription>
					<CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">1.234</CardTitle>
					<CardAction>
						<Badge variant="outline">
							<TrendingDown />
							-20%
						</Badge>
					</CardAction>
				</CardHeader>
				<CardFooter className="flex-col items-start gap-1.5 text-sm">
					<div className="line-clamp-1 flex gap-2 font-medium">
						Queda de 20% neste período <TrendingDown className="size-4" />
					</div>
					<div className="text-muted-foreground">Aquisição precisa de atenção</div>
				</CardFooter>
			</Card>
			<Card className="@container/card">
				<CardHeader>
					<CardDescription>Contas Ativas</CardDescription>
					<CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">45.678</CardTitle>
					<CardAction>
						<Badge variant="outline">
							<TrendingUp />
							+12,5%
						</Badge>
					</CardAction>
				</CardHeader>
				<CardFooter className="flex-col items-start gap-1.5 text-sm">
					<div className="line-clamp-1 flex gap-2 font-medium">
						Forte retenção de usuários <TrendingUp className="size-4" />
					</div>
					<div className="text-muted-foreground">Engajamento supera metas</div>
				</CardFooter>
			</Card>
			<Card className="@container/card">
				<CardHeader>
					<CardDescription>Taxa de Crescimento</CardDescription>
					<CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">4,5%</CardTitle>
					<CardAction>
						<Badge variant="outline">
							<TrendingUp />
							+4,5%
						</Badge>
					</CardAction>
				</CardHeader>
				<CardFooter className="flex-col items-start gap-1.5 text-sm">
					<div className="line-clamp-1 flex gap-2 font-medium">
						Aumento constante de performance <TrendingUp className="size-4" />
					</div>
					<div className="text-muted-foreground">Atende projeções de crescimento</div>
				</CardFooter>
			</Card>
		</div>
	);
};
