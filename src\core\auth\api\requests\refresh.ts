"use server";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { AUTH_ENDPOINTS } from "../endpoints";

import { createPostRequest } from "@/shared/lib/requests";

import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { AUTH_RETRY_CONFIG, AUTH_TIMEOUTS } from "../../constants/auth-timeouts";

export async function refreshRequest(): Promise<ApiResponse<IMessageGlobalReturn>> {
	const response = await createPostRequest<IMessageGlobalReturn>(
		AUTH_ENDPOINTS.REFRESH,
		{},
		{
			timeout: AUTH_TIMEOUTS.REFRESH_REQUEST,
			retry: AUTH_RETRY_CONFIG.DEFAULT_RETRY,
			retryAttempts: AUTH_RETRY_CONFIG.REFRESH_MAX_RETRIES,
		}
	);
	if (!response.success) return response;
	return response;
}
