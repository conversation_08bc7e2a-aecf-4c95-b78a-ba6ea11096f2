import { Modal } from "../../../../../../../shared/components/custom/modal";
import { useCreateCellComponentForm } from "../../../../../hooks/cell-components/create/form.hook";
import { CreateInspectionCellComponentForm } from "./form";

interface IModalCreateCellComponentProps {
	isOpen: boolean;
	onClose: () => void;
}

export const ModalCreateCellComponent = ({ isOpen, onClose }: IModalCreateCellComponentProps) => {
	const methods = useCreateCellComponentForm();

	const handleSubmit = () => {};

	const handleClose = () => {
		onClose();
		methods.reset();
	};

	return (
		<Modal isOpen={isOpen} onClose={handleClose} title="Cadastro de Célula por Componente">
			<CreateInspectionCellComponentForm methods={methods} onSubmit={handleSubmit} onClose={handleClose} />
		</Modal>
	);
};
