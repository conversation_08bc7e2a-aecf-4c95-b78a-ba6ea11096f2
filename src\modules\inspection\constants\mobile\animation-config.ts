import { Transition, Variants } from "framer-motion";

export const ANIMATION_DURATIONS = {
	FAST: 0.15,
	NORMAL: 0.2,
	SLOW: 0.3,
	REORDER: 0.25,
} as const;

export const ANIMATION_EASINGS = {
	EASE_OUT: "easeOut",
	EASE_IN: "easeIn",
	EASE_IN_OUT: "easeInOut",
	SPRING: "spring",
} as const;

export const SPRING_CONFIGS = {
	GENTLE: {
		type: "spring" as const,
		stiffness: 300,
		damping: 30,
	},
	RESPONSIVE: {
		type: "spring" as const,
		stiffness: 400,
		damping: 25,
	},
	SNAPPY: {
		type: "spring" as const,
		stiffness: 500,
		damping: 20,
	},
} as const;

export const FIELD_CARD_VARIANTS: Variants = {
	hidden: {
		opacity: 0,
		y: 10,
		scale: 0.95,
	},
	visible: {
		opacity: 1,
		y: 0,
		scale: 1,
		transition: {
			duration: ANIMATION_DURATIONS.NORMAL,
			ease: ANIMATION_EASINGS.EASE_OUT,
		},
	},
	exit: {
		opacity: 0,
		y: -10,
		scale: 0.95,
		transition: {
			duration: ANIMATION_DURATIONS.FAST,
			ease: ANIMATION_EASINGS.EASE_IN,
		},
	},
	reordering: {
		scale: 0.98,
		transition: {
			duration: ANIMATION_DURATIONS.FAST,
		},
	},
	tap: {
		scale: 0.98,
		transition: {
			duration: 0.1,
		},
	},
};

export const GROUP_VARIANTS: Variants = {
	hidden: {
		opacity: 0,
		y: 20,
	},
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: ANIMATION_DURATIONS.NORMAL,
			ease: ANIMATION_EASINGS.EASE_OUT,
		},
	},
	exit: {
		opacity: 0,
		y: -20,
		transition: {
			duration: ANIMATION_DURATIONS.NORMAL,
			ease: ANIMATION_EASINGS.EASE_IN,
		},
	},
};

export const LIST_CONTAINER_VARIANTS: Variants = {
	hidden: {
		opacity: 0,
	},
	visible: {
		opacity: 1,
		transition: {
			staggerChildren: 0.05,
			delayChildren: 0.1,
		},
	},
	exit: {
		opacity: 0,
		transition: {
			staggerChildren: 0.02,
			staggerDirection: -1,
		},
	},
};

export const LAYOUT_TRANSITION: Transition = {
	type: "spring",
	stiffness: 350,
	damping: 30,
	mass: 1,
};

export const ANIMATE_PRESENCE_CONFIG = {
	mode: "popLayout" as const,
	initial: false,
};

export const ACTION_BUTTON_VARIANTS: Variants = {
	idle: {
		scale: 1,
		opacity: 0.7,
	},
	hover: {
		scale: 1.05,
		opacity: 1,
		transition: {
			duration: ANIMATION_DURATIONS.FAST,
		},
	},
	tap: {
		scale: 0.95,
		transition: {
			duration: 0.1,
		},
	},
	disabled: {
		scale: 1,
		opacity: 0.3,
		transition: {
			duration: ANIMATION_DURATIONS.FAST,
		},
	},
};

export const LOADING_VARIANTS: Variants = {
	loading: {
		opacity: [0.5, 1, 0.5],
		transition: {
			duration: 1.5,
			repeat: Infinity,
			ease: "easeInOut",
		},
	},
};

export const ERROR_VARIANTS: Variants = {
	error: {
		x: [-2, 2, -2, 2, 0],
		transition: {
			duration: 0.4,
		},
	},
};

export const SUCCESS_VARIANTS: Variants = {
	success: {
		scale: [1, 1.05, 1],
		transition: {
			duration: 0.3,
		},
	},
};

export const RESPONSIVE_ANIMATION_CONFIG = {
	REDUCED_MOTION: {
		duration: 0.01,
		ease: "linear" as const,
	},
	MOBILE: {
		duration: ANIMATION_DURATIONS.FAST,
		ease: ANIMATION_EASINGS.EASE_OUT,
	},
	DESKTOP: {
		duration: ANIMATION_DURATIONS.NORMAL,
		ease: ANIMATION_EASINGS.EASE_IN_OUT,
	},
} as const;

export const getAnimationConfig = (context: "mobile" | "desktop" | "reduced") => {
	switch (context) {
		case "mobile":
			return RESPONSIVE_ANIMATION_CONFIG.MOBILE;
		case "desktop":
			return RESPONSIVE_ANIMATION_CONFIG.DESKTOP;
		case "reduced":
			return RESPONSIVE_ANIMATION_CONFIG.REDUCED_MOTION;
		default:
			return RESPONSIVE_ANIMATION_CONFIG.MOBILE;
	}
};
