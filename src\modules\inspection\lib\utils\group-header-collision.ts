import { CollisionDetection, rectIntersection } from "@dnd-kit/core";

/**
 * Detecção de colisão customizada para grupos de campos.
 *
 * PROBLEMA RESOLVIDO:
 * - Grupos com muitos itens ficavam difíceis de trocar de posição
 * - A área de colisão era muito grande (todo o grupo)
 * - Itens "empty" também tinham detecção imprecisa
 * - Com múltiplos grupos, querySelector encontrava elementos incorretos
 *
 * SOLUÇÃO:
 * - Para grupos com header: apenas 50% da altura do header ativa a troca
 * - Para itens "empty": detecção melhorada com tolerância de 10px nas bordas
 * - Usa coordenadas reais do DOM quando possível
 * - Busca mais específica por data-group-id para evitar conflitos entre grupos
 *
 * COMO FUNCIONA:
 * 1. Identifica se é um grupo com header ou item "empty"
 * 2. Para grupos: busca o elemento header no DOM usando data-group-id específico
 * 3. Para grupos: define zona de ativação = 50% da altura do header a partir do topo
 * 4. Para empty: usa toda a altura do item com tolerância de 10px nas bordas
 * 5. Calcula distância do centro para priorizar colisões mais próximas
 * 6. Fallback para busca global no documento quando necessário
 */
export const groupHeaderCollisionDetection: CollisionDetection = args => {
	const { droppableContainers, active, pointerCoordinates } = args;

	if (!active || !pointerCoordinates) {
		return rectIntersection(args);
	}

	const collisions = [];

	for (const droppableContainer of droppableContainers) {
		const { id, rect, node } = droppableContainer;
		const droppableId = String(id);

		// Se for um grupo "empty", usa uma detecção mais sensível
		if (droppableId.startsWith("empty")) {
			if (rect.current) {
				const fullRect = rect.current;

				// Para itens empty, considera toda a altura mas com uma margem de tolerância
				const tolerance = 10; // 10px de tolerância nas bordas
				const isWithinX = pointerCoordinates.x >= fullRect.left - tolerance && pointerCoordinates.x <= fullRect.left + fullRect.width + tolerance;

				const isWithinY = pointerCoordinates.y >= fullRect.top - tolerance && pointerCoordinates.y <= fullRect.top + fullRect.height + tolerance;

				if (isWithinX && isWithinY) {
					// Calcula distância do centro para priorizar colisões mais próximas
					const centerX = fullRect.left + fullRect.width / 2;
					const centerY = fullRect.top + fullRect.height / 2;
					const distance = Math.sqrt(Math.pow(pointerCoordinates.x - centerX, 2) + Math.pow(pointerCoordinates.y - centerY, 2));

					collisions.push({
						id,
						data: {
							droppableContainer,
							value: distance,
						},
					});
				}
			}
			continue;
		}

		// Para grupos com título, tenta encontrar o header no DOM
		if (rect.current && node.current) {
			// Busca pelo header específico do grupo no DOM de forma mais precisa
			// Primeiro tenta buscar diretamente no node do container
			let headerElement = node.current.querySelector(`[data-group-id="${droppableId}"][data-group-header="true"]`);

			// Se não encontrar, tenta buscar no documento inteiro (fallback para casos onde o DOM está aninhado)
			if (!headerElement) {
				headerElement = document.querySelector(`[data-group-id="${droppableId}"][data-group-header="true"]`);
			}

			if (headerElement) {
				// Usa as dimensões reais do header encontrado no DOM
				const headerRect = headerElement.getBoundingClientRect();

				// Verifica se o pointer está dentro da área do header
				const isWithinHeaderX = pointerCoordinates.x >= headerRect.left && pointerCoordinates.x <= headerRect.right;

				const isWithinHeaderY = pointerCoordinates.y >= headerRect.top && pointerCoordinates.y <= headerRect.bottom;

				if (isWithinHeaderX && isWithinHeaderY) {
					// Calcula a área de ativação (50% da altura do header a partir do topo)
					const activationHeight = headerRect.height * 0.5;
					const isInActivationZone = pointerCoordinates.y >= headerRect.top && pointerCoordinates.y <= headerRect.top + activationHeight;

					if (isInActivationZone) {
						// Calcula a distância do centro da zona de ativação ao pointer
						const centerX = headerRect.left + headerRect.width / 2;
						const centerY = headerRect.top + activationHeight / 2;
						const distance = Math.sqrt(Math.pow(pointerCoordinates.x - centerX, 2) + Math.pow(pointerCoordinates.y - centerY, 2));

						collisions.push({
							id,
							data: {
								droppableContainer,
								value: distance,
							},
						});
					}
				}
			} else {
				// Fallback: usa aproximação com altura de ativação de 25px (50% de 50px)
				const activationHeight = 25;
				const fullRect = rect.current;

				const isWithinHeaderX = pointerCoordinates.x >= fullRect.left && pointerCoordinates.x <= fullRect.left + fullRect.width;

				const isWithinActivationZone = pointerCoordinates.y >= fullRect.top && pointerCoordinates.y <= fullRect.top + activationHeight;

				if (isWithinHeaderX && isWithinActivationZone) {
					const centerX = fullRect.left + fullRect.width / 2;
					const centerY = fullRect.top + activationHeight / 2;
					const distance = Math.sqrt(Math.pow(pointerCoordinates.x - centerX, 2) + Math.pow(pointerCoordinates.y - centerY, 2));

					collisions.push({
						id,
						data: {
							droppableContainer,
							value: distance,
						},
					});
				}
			}
		}
	}

	// Ordena por distância (menor distância = maior prioridade)
	return collisions.sort((a, b) => {
		const aValue = a.data?.value ?? Infinity;
		const bValue = b.data?.value ?? Infinity;
		return aValue - bValue;
	});
};
