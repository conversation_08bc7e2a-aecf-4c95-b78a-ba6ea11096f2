import { useQuery } from "@tanstack/react-query";
import { createGetRequest } from "../../../../../shared/lib/requests";
import { IResponsePaginated } from "../../../../../shared/types/requests/response-paginated.type";
import { CELL_BY_PRODUCT_TYPE_ENDPOINTS } from "../../../api/endpoints";
import { inspectionKeys } from "../../../constants/query/keys";
import { ICellByProductTypeParamsDto, IInspectionCellByProductType } from "../../../types/cell-by-product-type/dtos/find-all.dto";

export const useFindAllInspectionCellByProductType = ({ page = 1, limit = 10, search = "" }: ICellByProductTypeParamsDto) => {
	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.cellByProductType.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<IInspectionCellByProductType>>(CELL_BY_PRODUCT_TYPE_ENDPOINTS.FIND_ALL({ page, limit, search })),
	});
	const isNoDataFound = !data?.success && data?.status === 404;

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
