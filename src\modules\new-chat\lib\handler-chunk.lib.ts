import { IChatMessage } from "../types/messages.type";
import { IChatStreamResponse } from "../types/streaming.type";

interface IStreamingChunkHandler {
	chunkData: IChatStreamResponse;
	updateMessage: (id: string, updates: Partial<IChatMessage>) => void;
	assistantMessageId: string;
	currentValue: string;
}

export function handleStreamingChunk({ chunkData, updateMessage, assistantMessageId, currentValue }: IStreamingChunkHandler) {
	const messageUpdates: Partial<IChatMessage> = {
		content: currentValue,
	};

	if (chunkData.type === "session") {
		messageUpdates.isStreaming = true;
	} else if (chunkData.type === "complete") {
		messageUpdates.isStreaming = false;
	} else {
		messageUpdates.isStreaming = true;
	}

	updateMessage(assistantMessageId, messageUpdates);
}
