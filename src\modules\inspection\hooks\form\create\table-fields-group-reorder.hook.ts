"use client";
import { reorderFieldGroupsAtom } from "@/modules/inspection/atoms/forms/fields/group-actions.atom";
import { fieldsGroupsAtom } from "@/modules/inspection/atoms/forms/fields/group.atom";
import { IFieldGroup } from "@/modules/inspection/types/forms/fields-table/fields-group.type";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useCallback, useState } from "react";


export const useTableFieldsGroupReorder = () => {
	const fieldsGroups = useAtomValue(fieldsGroupsAtom);
	const reorderGroups = useSetAtom(reorderFieldGroupsAtom);
	const [isReordering, setIsReordering] = useState(false);
	const [reorderingGroup, setReorderingGroup] = useState<string | null>(null);


	const handleMoveGroupUp = useCallback(
		async (group: IFieldGroup) => {
			const groupIndex = fieldsGroups.findIndex(g => g.tempId === group.tempId);
			if (groupIndex === 0 || isReordering) return;

			setIsReordering(true);
			setReorderingGroup(group.tempId);

			try {
				await new Promise(resolve => setTimeout(resolve, 150));

				reorderGroups({
					fromIndex: groupIndex,
					toIndex: groupIndex - 1,
				});
			} finally {
				setTimeout(() => {
					setIsReordering(false);
					setReorderingGroup(null);
				}, 100);
			}
		},
		[fieldsGroups, reorderGroups, isReordering],
	);

	const handleMoveGroupDown = useCallback(
		async (group: IFieldGroup) => {
			const groupIndex = fieldsGroups.findIndex(g => g.tempId === group.tempId);
			if (groupIndex === fieldsGroups.length - 1 || isReordering) return;

			setIsReordering(true);
			setReorderingGroup(group.tempId);

			try {
				await new Promise(resolve => setTimeout(resolve, 150));

				reorderGroups({
					fromIndex: groupIndex,
					toIndex: groupIndex + 1,
				});
			} finally {
				setTimeout(() => {
					setIsReordering(false);
					setReorderingGroup(null);
				}, 100);
			}
		},
		[fieldsGroups, reorderGroups, isReordering],
	);

	const canMoveGroupUp = useCallback(
		(group: IFieldGroup): boolean => {
			const groupIndex = fieldsGroups.findIndex(g => g.tempId === group.tempId);
			return groupIndex > 0 && !isReordering;
		},
		[fieldsGroups, isReordering],
	);

	const canMoveGroupDown = useCallback(
		(group: IFieldGroup): boolean => {
			const groupIndex = fieldsGroups.findIndex(g => g.tempId === group.tempId);
			return groupIndex < fieldsGroups.length - 1 && !isReordering;
		},
		[fieldsGroups, isReordering],
	);

	const isGroupReordering = useCallback(
		(groupId: string): boolean => {
			return reorderingGroup === groupId;
		},
		[reorderingGroup],
	);

	return {
		fieldsGroups,
		isReordering,
		reorderingGroup,
		handleMoveGroupUp,
		handleMoveGroupDown,
		canMoveGroupUp,
		canMoveGroupDown,
		isGroupReordering,
	};
};
