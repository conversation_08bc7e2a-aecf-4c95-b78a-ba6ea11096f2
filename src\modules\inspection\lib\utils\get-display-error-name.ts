export const getFieldDisplayName = (fieldPath: string): string => {
	const fieldNames: Record<string, string> = {
		"field.id": "ID do Campo",
		"field.name": "Nome do Campo",
		nickname: "<PERSON>pel<PERSON>",
		sequence: "<PERSON>qu<PERSON><PERSON>",
		typeId: "Tipo",
		"measure.id": "ID da Medida",
		"measure.name": "Nome da Medida",
		groupTitle: "Título do Grupo",
		options: "Opções",
		required: "Campo Obrigatório",
		biFilter: "Filtro BI",
	};

	return fieldNames[fieldPath] || fieldPath;
};
