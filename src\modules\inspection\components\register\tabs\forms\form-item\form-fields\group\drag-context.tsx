import { DraggableAttributes } from "@dnd-kit/core";
import { SyntheticListenerMap } from "@dnd-kit/core/dist/hooks/utilities";
import { createContext, useContext } from "react";

interface GroupDragContextType {
	groupDragProps?: {
		attributes: DraggableAttributes;
		listeners: SyntheticListenerMap | undefined;
	};
	isOnlyItem?: boolean;
}

export const GroupDragContext = createContext<GroupDragContextType>({});
export const useGroupDragContext = () => useContext(GroupDragContext);
