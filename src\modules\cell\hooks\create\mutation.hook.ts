import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "../../../../core/toast";
import { createPostRequest } from "../../../../shared/lib/requests";
import { IMessageGlobalReturn } from "../../../../shared/types/requests/message.type";
import { CELL_ENDPOINTS } from "../../api/endpoints";
import { cellQueryKeys } from "../../constants/query";
import { TCreateCell } from "../../validators/create";

export const useCreateCellMutation = () => {
	const queryClient = useQueryClient();

	const createMutation = useMutation({
		mutationKey: cellQueryKeys.custom("create"),
		mutationFn: async (cell: TCreateCell) => {
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(CELL_ENDPOINTS.CREATE, cell);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => cellQueryKeys.invalidateAll(queryClient),
	});

	return {
		createCell: (form: TCreateCell) =>
			toast.promise(createMutation.mutateAsync(form), {
				loading: "Criando célula...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
