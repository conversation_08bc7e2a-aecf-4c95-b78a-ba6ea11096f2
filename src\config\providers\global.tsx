"use client";

import { UserProviderWithLoading } from "@/core/auth/providers/user.provider";
import { ToastContainer } from "@/core/toast";
import { IReactChildrenType } from "@/shared/types/components/react-children.type";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { createStore, Provider } from "jotai";

import { Chat } from "../../modules/new-chat/components";
import { QueryProvider } from "./query";

export const SIMP_STORE = createStore();

export const ProviderGlobal = ({ children }: IReactChildrenType) => {
	return (
		<Provider store={SIMP_STORE}>
			<QueryProvider>
				<UserProviderWithLoading>
					{children}
					<Chat />
					<ToastContainer />
				</UserProviderWithLoading>
				<ReactQueryDevtools buttonPosition="top-right" initialIsOpen={false} />
			</QueryProvider>
		</Provider>
	);
};
