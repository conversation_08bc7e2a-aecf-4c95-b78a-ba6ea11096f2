import { NextRequest, NextResponse } from "next/server";
import { authMiddleware } from "./config/middleware/auth/auth.middleware";
import { permissionsMiddleware } from "./config/middleware/auth/route.middleware";
import { SecurityHeadersMiddleware } from "./config/middleware/security/security-headers.middleware";

const isRedirect = (response: Response): boolean => {
	return (
		response.status === 302 ||
		!!response.headers.get("x-middleware-rewrite") ||
		!!response.headers.get("x-middleware-redirect") ||
		!!response.headers.get("location")
	);
};

export async function middleware(request: NextRequest): Promise<Response> {
	const { pathname } = request.nextUrl;
	if (pathname.startsWith("/auth/")) return NextResponse.next();
	const authResponse = await authMiddleware(request);
	if (isRedirect(authResponse)) return authResponse;
	const permissionsResponse = permissionsMiddleware(request);
	if (isRedirect(permissionsResponse)) return SecurityHeadersMiddleware.addToResponse(permissionsResponse);
	return SecurityHeadersMiddleware.addToResponse(NextResponse.next());
}

export const config = {
	matcher: ["/((?!api|_next/static|_next/image|_next/font|favicon.ico|events/devtools/events|events$|\\.well-known).*)"],
};
