import { useEffect, useRef } from "react";

export function useTableFieldsSyncScroll() {
	const headerRef = useRef<HTMLDivElement>(null);
	const bodyRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const header = headerRef.current;
		const body = bodyRef.current;
		if (!header || !body) return;
		const syncScroll = (from: HTMLElement, to: HTMLElement) => () => (to.scrollLeft = from.scrollLeft);
		header.addEventListener("scroll", syncScroll(header, body));
		body.addEventListener("scroll", syncScroll(body, header));
		return () => {
			header.removeEventListener("scroll", syncScroll(header, body));
			body.removeEventListener("scroll", syncScroll(body, header));
		};
	}, []);

	return { headerRef, bodyRef };
}
