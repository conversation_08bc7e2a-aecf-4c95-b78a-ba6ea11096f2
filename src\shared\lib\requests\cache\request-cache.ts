import { CACHE_TTL, MAX_CACHE_SIZE } from "../constants";

interface ICacheEntry<T> {
	data: T;
	timestamp: number;
	ttl: number;
}

export class RequestCache {
	private readonly cache = new Map<string, ICacheEntry<unknown>>();
	private readonly accessOrder: string[] = [];

	get<T>(key: string): T | null {
		const entry = this.cache.get(key);
		if (!entry) return null;

		if (Date.now() - entry.timestamp > entry.ttl) {
			this.delete(key);
			return null;
		}
		this.updateAccessOrder(key);

		return entry.data as T;
	}

	set<T>(key: string, data: T, ttl: number = CACHE_TTL.MEDIUM): void {
		if (this.cache.size >= MAX_CACHE_SIZE && !this.cache.has(key)) {
			const oldestKey = this.accessOrder[0];
			this.delete(oldestKey);
		}

		this.cache.set(key, {
			data,
			timestamp: Date.now(),
			ttl,
		});

		this.updateAccessOrder(key);
	}

	delete(key: string): boolean {
		const deleted = this.cache.delete(key);
		if (deleted) {
			const index = this.accessOrder.indexOf(key);
			if (index > -1) this.accessOrder.splice(index, 1);
		}
		return deleted;
	}

	clear(): void {
		this.cache.clear();
		this.accessOrder.length = 0;
	}

	has(key: string): boolean {
		const entry = this.cache.get(key);
		if (!entry) return false;
		if (Date.now() - entry.timestamp > entry.ttl) {
			this.delete(key);
			return false;
		}
		return true;
	}

	size(): number {
		return this.cache.size;
	}

	prefetch<T>(key: string, fetcher: () => Promise<T>, ttl?: number): Promise<void> {
		return fetcher()
			.then(data => {
				this.set(key, data, ttl);
			})
			.catch(() => {});
	}

	private updateAccessOrder(key: string): void {
		const index = this.accessOrder.indexOf(key);
		if (index > -1) this.accessOrder.splice(index, 1);
		this.accessOrder.push(key);
	}

	startCleanup(intervalMs: number = 60000): () => void {
		const intervalId = setInterval(() => {
			for (const [key, entry] of this.cache.entries()) {
				if (Date.now() - entry.timestamp > entry.ttl) {
					this.delete(key);
				}
			}
		}, intervalMs);
		// Retornar função para parar a limpeza
		return () => clearInterval(intervalId);
	}
}
