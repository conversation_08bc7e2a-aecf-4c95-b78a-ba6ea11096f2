"use client";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createGetRequest } from "@/shared/lib/requests";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { IHookPaginatedProps } from "../../../../../shared/types/pagination/types";
import { inspectionKeys } from "../../../constants/query/keys";
import { IInspectionFormDto } from "../../../types/forms/dtos/find-all.dto";

export const useFindAllInspectionForm = ({ page = 1, limit = 10, search = "" }: IHookPaginatedProps = {}) => {
	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.forms.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<IInspectionFormDto>>(INSPECTION_FORM_ENDPOINTS.FIND_ALL({ page, limit, search })),
	});

	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success,
		error: !data?.success && data?.data.message,
	};
};
