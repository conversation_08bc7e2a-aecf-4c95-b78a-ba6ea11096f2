"use client";

import { ApiResponse } from "@/shared/types/requests/request.type";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { useCallback, useEffect, useState } from "react";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { userAtom } from "../../atoms/user.atom";
import { authQueryKeys } from "../../constants/query";
import { getAuthToken } from "../../lib/auth-actions";
import { getCurrentUser } from "../../lib/user-actions";
import { IUser } from "../../types/user.types";

export function useUserSync() {
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);
	const queryClient = useQueryClient();
	const [hasToken, setHasToken] = useState<boolean | null>(null);

	const clearUserData = useCallback(() => {
		setIsAuthenticated(false);
		setUser(null);
	}, [setIsAuthenticated, setUser]);

	const updateAuthState = useCallback(
		(userData: ApiResponse<IUser>) => {
			const isValid = !!(userData?.success && userData?.data);
			setIsAuthenticated(isValid);
			setUser(isValid ? userData.data : null);
		},
		[setIsAuthenticated, setUser],
	);

	useEffect(() => {
		const checkToken = async () => {
			const token = await getAuthToken();
			setHasToken(!!token);
			if (!token) clearUserData();
		};

		checkToken();
	}, [clearUserData]);

	const { data, isLoading, error } = useQuery({
		queryKey: authQueryKeys.user.all(),
		queryFn: getCurrentUser,
		enabled: hasToken === true,
		retry: false,
		staleTime: 5 * 60 * 1000,
		gcTime: 10 * 60 * 1000,
	});

	useEffect(() => {
		if (hasToken === false) {
			clearUserData();
		} else if (data) {
			updateAuthState(data);
		}
	}, [data, hasToken, clearUserData, updateAuthState]);

	const refreshUser = async () => {
		const token = await getAuthToken();
		if (token) {
			authQueryKeys.user.invalidateAll(queryClient);
		} else {
			setHasToken(false);
			clearUserData();
		}
	};

	return {
		user: data?.data ?? null,
		isLoading: hasToken === null || (hasToken && isLoading),
		error,
		isAuthenticated: hasToken === true && !!data?.success && !!data?.data,
		refreshUser,
	};
}
