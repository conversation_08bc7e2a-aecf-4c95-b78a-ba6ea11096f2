[data-has-errors="true"] button[role="combobox"],
[data-has-errors="true"] .select-trigger,
[data-has-errors="true"] input,
[data-has-errors="true"] textarea,
[data-has-errors="true"] select {
	border-color: rgb(252, 165, 165) !important;
	box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.2) !important;
}

[data-has-errors="true"] button[role="combobox"]:focus,
[data-has-errors="true"] .select-trigger:focus,
[data-has-errors="true"] input:focus,
[data-has-errors="true"] textarea:focus,
[data-has-errors="true"] select:focus {
	border-color: rgb(239, 68, 68) !important;
	box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}

[data-has-errors="true"] [data-radix-select-trigger] {
	border-color: rgb(252, 165, 165) !important;
	box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.2) !important;
}

[data-has-errors="true"] [data-radix-select-trigger]:focus {
	border-color: rgb(239, 68, 68) !important;
	box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}
