"use client";
import { atom } from "jotai";
import { IChatSession } from "../../types/session.type";
import { chatStreamingSessionInfo } from "./info.atom";

export const createNewSessionAtom = atom(null, (_, set) => {
	const newSession: IChatSession = {
		id: crypto.randomUUID(),
		messages: [],
		createdAt: new Date(),
		updatedAt: new Date(),
	};
	set(chatStreamingSessionInfo, newSession);
	return newSession;
});
