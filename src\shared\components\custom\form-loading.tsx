import { Skeleton } from "@/shared/components/shadcn/skeleton";
import { cn } from "@/shared/lib/shadcn/utils";
import { Loader2 } from "lucide-react";

interface FormLoadingProps {
	className?: string;
	message?: string;
	variant?: "skeleton" | "spinner" | "full";
}

export const FormLoading: React.FC<FormLoadingProps> = ({ className, message = "Carregando formulário...", variant = "full" }) => {
	if (variant === "skeleton") {
		return (
			<div className={cn("space-y-6 p-6", className)}>
				{/* Header skeleton */}
				<div className="space-y-2">
					<Skeleton className="h-8 w-3/4" />
					<Skeleton className="h-4 w-1/2" />
				</div>

				{/* Form fields skeleton */}
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					{Array.from({ length: 6 }).map((_, index) => (
						<div key={index} className="space-y-2">
							<Skeleton className="h-4 w-24" />
							<Skeleton className="h-10 w-full" />
						</div>
					))}
				</div>

				{/* Dynamic fields skeleton */}
				<div className="space-y-4">
					<Skeleton className="h-6 w-48" />
					<div className="space-y-3">
						{Array.from({ length: 3 }).map((_, index) => (
							<div key={index} className="space-y-3 rounded-lg border p-4">
								<div className="flex items-center justify-between">
									<Skeleton className="h-5 w-32" />
									<Skeleton className="h-8 w-20" />
								</div>
								<div className="grid grid-cols-2 gap-3">
									<Skeleton className="h-10 w-full" />
									<Skeleton className="h-10 w-full" />
								</div>
							</div>
						))}
					</div>
				</div>

				{/* Buttons skeleton */}
				<div className="flex justify-end gap-3 pt-4">
					<Skeleton className="h-10 w-24" />
					<Skeleton className="h-10 w-32" />
				</div>
			</div>
		);
	}

	if (variant === "spinner") {
		return (
			<div className={cn("flex items-center justify-center p-8", className)}>
				<div className="flex flex-col items-center space-y-4">
					<Loader2 className="text-primary h-8 w-8 animate-spin" />
					<p className="text-muted-foreground text-sm">{message}</p>
				</div>
			</div>
		);
	}

	// Full variant - combines both approaches
	return (
		<div className={cn("relative", className)}>
			{/* Overlay with spinner */}
			<div className="bg-background/80 absolute inset-0 z-10 flex items-center justify-center backdrop-blur-sm">
				<div className="bg-card flex flex-col items-center space-y-4 rounded-lg border p-6 shadow-lg">
					<div className="relative">
						<Loader2 className="text-primary h-8 w-8 animate-spin" />
						<div className="border-primary/20 absolute inset-0 animate-pulse rounded-full border-2" />
					</div>
					<div className="space-y-1 text-center">
						<p className="text-sm font-medium">{message}</p>
						<p className="text-muted-foreground text-xs">Por favor, aguarde...</p>
					</div>
					{/* Loading dots animation */}
					<div className="flex space-x-1">
						{Array.from({ length: 3 }).map((_, index) => (
							<div
								key={index}
								className="bg-primary h-2 w-2 animate-bounce rounded-full"
								style={{
									animationDelay: `${index * 0.1}s`,
									animationDuration: "0.6s",
								}}
							/>
						))}
					</div>
				</div>
			</div>

			{/* Background skeleton content */}
			<div className="space-y-6 p-6 opacity-50">
				<div className="space-y-2">
					<Skeleton className="h-8 w-3/4" />
					<Skeleton className="h-4 w-1/2" />
				</div>

				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					{Array.from({ length: 6 }).map((_, index) => (
						<div key={index} className="space-y-2">
							<Skeleton className="h-4 w-24" />
							<Skeleton className="h-10 w-full" />
						</div>
					))}
				</div>

				<div className="space-y-4">
					<Skeleton className="h-6 w-48" />
					<div className="space-y-3">
						{Array.from({ length: 2 }).map((_, index) => (
							<div key={index} className="space-y-3 rounded-lg border p-4">
								<div className="flex items-center justify-between">
									<Skeleton className="h-5 w-32" />
									<Skeleton className="h-8 w-20" />
								</div>
								<div className="grid grid-cols-2 gap-3">
									<Skeleton className="h-10 w-full" />
									<Skeleton className="h-10 w-full" />
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		</div>
	);
};
