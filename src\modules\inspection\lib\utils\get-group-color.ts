const GROUP_COLORS = [
	{
		name: "blue",
		light: "bg-[#3B82F6]/15 border-[#3B82F6]/15",
		medium: "bg-[#3B82F6]/30",
		accent: "bg-[#3B82F6]/45",
		line: "bg-[#3B82F6]/45",
	},
	{
		name: "green",
		light: "bg-[#34D399]/15 border-[#34D399]/15",
		medium: "bg-[#34D399]/30",
		accent: "bg-[#34D399]/45",
		line: "bg-[#34D399]/45",
	},
] as const;

export type GroupColorVariant = "light" | "medium" | "accent" | "line";

export const getGroupColorClasses = (indexInGroup: number, variant: GroupColorVariant = "accent"): string => {
	const colorIndex = (indexInGroup - 1) % GROUP_COLORS.length;
	const colorScheme = GROUP_COLORS[colorIndex];
	return colorScheme[variant];
};

export const getGroupColor = (indexInGroup: number): string => {
	const colorIndex = (indexInGroup - 1) % GROUP_COLORS.length;
	return GROUP_COLORS[colorIndex].name;
};
