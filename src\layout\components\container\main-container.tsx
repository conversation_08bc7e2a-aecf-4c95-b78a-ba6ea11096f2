"use client";
import { SidebarInset, SidebarProvider } from "@/shared/components/shadcn/sidebar";
import { IReactChildrenType } from "@/shared/types/components/react-children.type";

import { AppSidebar } from "./app.sidebar";
import { Header } from "./header";

export const MainContainer = ({ children }: IReactChildrenType) => {
	return (
		<SidebarProvider
			style={
				{
					"--sidebar-width": "calc(var(--spacing) * 72)",
					"--header-height": "calc(var(--spacing) * 12)",
				} as React.CSSProperties
			}
		>
			<AppSidebar variant="inset" />
			<SidebarInset>
				<Header />
				<div className="flex flex-1 flex-col">
					<div className="@container/main flex flex-1 flex-col gap-2">
						<div className="flex h-full flex-col gap-4 py-4 md:gap-6 md:py-6">{children}</div>
					</div>
				</div>
			</SidebarInset>
		</SidebarProvider>
	);
};
