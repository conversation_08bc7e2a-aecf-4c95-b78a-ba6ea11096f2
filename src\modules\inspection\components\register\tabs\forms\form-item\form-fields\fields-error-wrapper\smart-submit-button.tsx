import { validateFields<PERSON>tom } from "@/modules/inspection/atoms/forms/fields/field-validation.atom";
import { Button } from "@/shared/components/shadcn/button";
import { cn } from "@/shared/lib/shadcn/utils";
import { useAtomValue } from "jotai";
import { Check, Save } from "lucide-react";
import React from "react";

interface SmartSubmitButtonProps {
	mode: "create" | "edit" | "view";
	isFormValid?: boolean;
	className?: string;
	onClick?: () => void;
	type?: "button" | "submit";
}

export const SmartSubmitButton: React.FC<SmartSubmitButtonProps> = ({ mode, className, onClick, type = "submit" }) => {
	const { hasErrors, hasFields, validationErrors } = useAtomValue(validateFieldsAtom);
	const isDisabled = hasErrors || !hasFields;
	const Icon = mode === "create" ? Check : Save;
	const text = mode === "create" ? "Criar" : "Salvar";

	const scrollToFirstError = () => {
		if (hasErrors && validationErrors[0]) {
			document.querySelector(`[data-field-id="${validationErrors[0].tempId}"]`)?.scrollIntoView({ behavior: "smooth", block: "center" });
		}
	};

	const handleClick = () => {
		if (isDisabled) {
			scrollToFirstError();
			return;
		}
		onClick?.();
	};

	return (
		<Button
			type={isDisabled ? "button" : type}
			variant="default"
			onClick={handleClick}
			disabled={isDisabled}
			className={cn("!disabled:cursor-not-allowed transition-all duration-200 disabled:opacity-40", className)}
		>
			<Icon className="mr-2 h-4 w-4" />
			{text}
		</Button>
	);
};
