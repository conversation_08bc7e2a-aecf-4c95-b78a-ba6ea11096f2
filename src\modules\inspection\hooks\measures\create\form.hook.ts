import { createMeasuresSchema, TCreateMeasures } from "@/modules/inspection/validators/measures/create";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, UseFormReturn } from "react-hook-form";

interface UseCreateMeasuresReturn {
	methods: UseFormReturn<TCreateMeasures>;
}

export default function useCreateMeasures(): UseCreateMeasuresReturn {
	const methods = useForm<TCreateMeasures>({
		resolver: zodResolver(createMeasuresSchema),
		defaultValues: {
			name: "",
			abbreviation: "",
		},
		mode: "onChange",
	});

	return { methods };
}
