import { MongoAbility, Subject } from "@casl/ability";
import { TPermissionAction } from "./actions";
import { TPermissionSubject } from "./subjects";

export type Actions = TPermissionAction;
export type Subjects = TPermissionSubject | Subject;
export type AppAbility = MongoAbility<[Actions, Subjects]>;

export interface IDefineCaslPermission {
	action: TPermissionAction;
	subject: TPermissionSubject;
}

export interface IAuthorizableEntity {
	id?: string;
	authorId?: string;
	userId?: string;
	ownerId?: string;
}
