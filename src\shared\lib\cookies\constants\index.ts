export const COOKIE_ERROR_MESSAGES = {
	INVALID_COOKIE: "O cookie não é válido no sistema",
	MISSING_NAME: "Nome do cookie é obrigatório",
	MISSING_VALUE: "Valor do cookie é obrigatório",
	NOT_FOUND: (name: string) => `O cookie ${name} não foi encontrado`,
	GENERIC_ERROR: (error: string) => `Erro ao processar cookie: ${error}`,
} as const;

export const COOKIE_SUCCESS_MESSAGES = {
	CREATED: (name: string) => `Cookie ${name} criado com sucesso`,
	REMOVED: (name: string) => `Cookie ${name} removido com sucesso`,
	FOUND: (name: string) => `Cookie ${name} encontrado`,
} as const;

export const HTTP_STATUS = {
	OK: 200,
	CREATED: 201,
	BAD_REQUEST: 400,
	NOT_FOUND: 404,
	INTERNAL_ERROR: 500,
} as const;
