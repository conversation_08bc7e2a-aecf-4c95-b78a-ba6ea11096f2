import { useFieldValidation } from "@/modules/inspection/hooks/form/error-fields-wrapper/use-field-validation.hook";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/shared/components/shadcn/tooltip";
import { AlertCircle, CheckCircle2, Info } from "lucide-react";
import React from "react";

interface ValidationTooltipProps {
	tempId: string;
	children: React.ReactNode;
	showOnValid?: boolean;
}

export const ValidationTooltip: React.FC<ValidationTooltipProps> = ({ tempId, children, showOnValid = false }) => {
	const validation = useFieldValidation(tempId);

	if (!validation.hasErrors && (!validation.isValid || !showOnValid)) {
		return <>{children}</>;
	}

	const getTooltipContent = () => {
		if (validation.hasErrors) {
			return (
				<div className="space-y-1">
					<div className="flex items-center gap-2 font-medium text-red-100">
						<AlertCircle className="h-3 w-3" />
						Campos obrigatórios
					</div>
					<ul className="space-y-1 text-xs text-red-200">
						{validation.errorMessages.map((error, index) => (
							<li key={index} className="flex items-start gap-1">
								<span className="mt-0.5 h-1 w-1 flex-shrink-0 rounded-full bg-red-300" />
								{error}
							</li>
						))}
					</ul>
				</div>
			);
		}

		if (validation.isValid && showOnValid) {
			return (
				<div className="flex items-center gap-2 text-green-100">
					<CheckCircle2 className="h-3 w-3" />
					Campo válido
				</div>
			);
		}

		return null;
	};

	return (
		<TooltipProvider>
			<Tooltip>
				<TooltipTrigger asChild>{children}</TooltipTrigger>
				<TooltipContent side="top" className={validation.hasErrors ? "border-red-700 bg-red-900" : "border-green-700 bg-green-900"}>
					{getTooltipContent()}
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
};

export const FieldHelpTooltip: React.FC<{ children: React.ReactNode; helpText: string }> = ({ children, helpText }) => {
	return (
		<TooltipProvider>
			<Tooltip>
				<TooltipTrigger asChild>
					<div className="flex items-center gap-1">
						{children}
						<Info className="text-muted-foreground h-3 w-3 cursor-help" />
					</div>
				</TooltipTrigger>
				<TooltipContent side="top">
					<div className="text-xs">{helpText}</div>
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
};
