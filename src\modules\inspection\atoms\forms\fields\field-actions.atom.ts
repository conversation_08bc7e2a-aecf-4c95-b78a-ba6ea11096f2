import { CREATE_DEFAULT_FIELD } from "@/modules/inspection/constants/form/default-field-value";
import { arrayMove } from "@dnd-kit/sortable";
import { atom } from "jotai";
import { getTotalItemsCountAtom } from "./group-actions.atom";
import { fieldsGroupsAtom } from "./group.atom";

export const addItemToGroupAtom = atom(null, (get, set, groupId: string) => {
	const groups = get(fieldsGroupsAtom);
	const totalItemsCount = get(getTotalItemsCountAtom);
	const newItem = CREATE_DEFAULT_FIELD(totalItemsCount + 1);

	set(
		fieldsGroupsAtom,
		groups.map(group => (group.tempId === groupId ? { ...group, items: [...group.items, newItem] } : group)),
	);
});

export const removeItemFromGroupAtom = atom(null, (get, set, { itemId }: { itemId: string }) => {
	const groups = get(fieldsGroupsAtom);

	set(
		fieldsGroupsAtom,
		groups.map(group => ({
			...group,
			items: group.items.filter(item => item.tempId !== itemId),
		})),
	);
});

export const reorderItemsInGroupAtom = atom(null, (get, set, { groupId, fromIndex, toIndex }: { groupId: string; fromIndex: number; toIndex: number }) => {
	const groups = get(fieldsGroupsAtom);

	const updatedGroups = groups.map(group => {
		if (group.tempId === groupId) {
			return { ...group, items: arrayMove(group.items, fromIndex, toIndex) };
		}
		return group;
	});

	let sequenceCounter = 1;
	const groupsWithUpdatedSequence = updatedGroups.map(group => {
		const updatedItems = group.items.map(item => ({
			...item,
			sequence: sequenceCounter++,
		}));
		return { ...group, items: updatedItems };
	});

	set(fieldsGroupsAtom, groupsWithUpdatedSequence);
});
