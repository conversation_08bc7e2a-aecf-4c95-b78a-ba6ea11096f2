export interface IUpdateFormDTO {
	title: string;
	text?: string;
	nomenclature: string;
	developerId: string;
	approverId: string;
	fields: IFieldForm[];
}

export interface IFieldForm {
	fieldId: number;
	nickname: string;
	required: boolean;
	group: number | undefined;
	sequence: number;
	typeId: number;
	measureId: number;
	groupTitle: string | undefined;
	biFilter: boolean;
	id?: number;
	options: IOption[];
}

interface IOption {
	sequence: number;
	option: string;
	id?: number;
}
