import { updateField<PERSON><PERSON><PERSON><PERSON> } from "@/modules/inspection/atoms/forms/fields/field-values.atom";
import { InspectionFormTypeEnum } from "@/modules/inspection/constants/form/type-enum";
import { ICreateFieldForm } from "@/modules/inspection/validators/form/create-field";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/shadcn/select";
import { Row } from "@tanstack/react-table";
import { useSetAtom } from "jotai";
import { FieldValidationWrapper } from "../fields-error-wrapper/field-validation-wrapper";

const typeLabels: Record<keyof typeof InspectionFormTypeEnum, string> = {
	INTEGER: "Inteiro",
	TEXT: "Texto",
	DATE: "Data",
	HOUR: "Hora",
	SECONDS: "Segundos",
	OPTIONS: "Opções",
	BOOLEAN: "Booleano",
	DECIMAL: "Decimal",
	DECIMAL2DIGITS: "Decimal (2 dígitos)",
	IMAGE: "Imagem",
};

export const InspectionFormFieldTypeRow = ({ row, mode }: { row: Row<ICreateFieldForm>; mode?: "create" | "edit" | "view" }) => {
	const { tempId, typeId } = row.original;
	const updateField = useSetAtom(updateFieldTypeAtom);

	return (
		<FieldValidationWrapper tempId={tempId} showValidationIcon={false} showErrorMessage={false}>
			<Select
				value={typeId?.toString()}
				disabled={mode === "view"}
				onValueChange={value => {
					const typeId = Number(value) as InspectionFormTypeEnum;
					updateField({ tempId, typeId });
				}}
			>
				<SelectTrigger disabled={mode === "view"} className="w-full bg-white **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate">
					<SelectValue placeholder="Selecione o tipo" />
				</SelectTrigger>
				<SelectContent align="end">
					{Object.entries(InspectionFormTypeEnum).map(([key, value]) => (
						<SelectItem key={value} value={value.toString()}>
							{typeLabels[key as keyof typeof InspectionFormTypeEnum] ?? key}
						</SelectItem>
					))}
				</SelectContent>
			</Select>
		</FieldValidationWrapper>
	);
};
