import { createFieldSchema } from "@/modules/inspection/validators/form/create-field";
import { atom } from "jotai";
import { ZodError } from "zod";
import { fieldsGroupsAtom } from "./group.atom";

export interface FieldValidationError {
	tempId: string;
	errors: Record<string, string[]>;
}

export const validateFieldsAtom = atom(get => {
	const fieldsGroups = get(fieldsGroupsAtom);
	const validationErrors: FieldValidationError[] = [];
	const allErrors: string[] = [];
	const validFields: string[] = [];

	fieldsGroups.forEach(group => {
		group.items.forEach(field => {
			try {
				createFieldSchema.parse(field);
				validFields.push(field.tempId);
			} catch (error) {
				if (error instanceof ZodError) {
					const fieldErrors: Record<string, string[]> = {};

					error.errors.forEach(err => {
						const path = err.path.join(".");
						if (!fieldErrors[path]) {
							fieldErrors[path] = [];
						}
						fieldErrors[path].push(err.message);
					});

					allErrors.push(...Object.values(fieldErrors).flat());

					validationErrors.push({
						tempId: field.tempId,
						errors: fieldErrors,
					});
				}
			}
		});
	});

	return {
		allErrors,
		validationErrors,
		validFields,
		hasErrors: validationErrors.length > 0,
		hasFields: fieldsGroups.some(group => group.items.length > 0),
	};
});

export const getFieldValidationErrorsAtom = atom(get => {
	return (tempId: string) => {
		const validation = get(validateFieldsAtom);
		return validation.validationErrors.find(error => error.tempId === tempId)?.errors || {};
	};
});

export const isFieldValidAtom = atom(get => {
	return (tempId: string) => {
		const validation = get(validateFieldsAtom);
		return validation.validFields.includes(tempId);
	};
});
