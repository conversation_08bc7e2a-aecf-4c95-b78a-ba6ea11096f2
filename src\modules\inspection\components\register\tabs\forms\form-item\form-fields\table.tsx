import { addField<PERSON>roup<PERSON><PERSON>, generateEmptyGroupWithItems } from "@/modules/inspection/atoms/forms/fields/group-actions.atom";
import { fieldsGroupsAtom } from "@/modules/inspection/atoms/forms/fields/group.atom";
import { useTableGroupsDrag } from "@/modules/inspection/hooks/form/create/table-groups-drag.hook";
import { useTableFieldsSyncScroll } from "@/modules/inspection/hooks/form/table/table-fields-sync-scroll.hook";
import { Button } from "@/shared/components/shadcn/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { closestCenter, DndContext, DragOverlay, MeasuringStrategy } from "@dnd-kit/core";
import { restrictToParentElement, restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { Folder, Plus } from "lucide-react";
import { useState } from "react";
import { inspectionFormColumns } from "./columns/columns";
import { GroupItem } from "./group/field-group";
import { GroupHeader } from "./group/group-header";

export interface ITableFieldsProps {
	mode?: "create" | "edit" | "view";
}

export const TableFields: React.FC<ITableFieldsProps> = ({ mode = "create" }) => {
	const fieldsGroups = useAtomValue(fieldsGroupsAtom);
	const addField = useSetAtom(generateEmptyGroupWithItems);
	const addGroup = useSetAtom(addFieldGroupAtom);
	const { handleDragEnd, sensors, sortableId, dataGroupsId } = useTableGroupsDrag();
	const [activeId, setActiveId] = useState<string | null>(null);
	const { headerRef, bodyRef } = useTableFieldsSyncScroll();

	const table = useReactTable({
		data: [],
		columns: inspectionFormColumns,
		getCoreRowModel: getCoreRowModel(),
	});

	const activeGroup = activeId ? fieldsGroups.find(g => g.tempId === activeId) : null;
	const lastGroupId = fieldsGroups.at(-1)?.tempId;

	return (
		<div className="mt-2 flex flex-col gap-4">
			<div className="hidden w-full items-center justify-end gap-2 lg:flex">
				<Button
					onClick={e => {
						e.preventDefault();
						addGroup();
					}}
					disabled={mode === "view"}
				>
					<Folder className="mr-2 size-4" />
					Adicionar grupo
				</Button>
				<Button
					variant="outline"
					onClick={e => {
						e.preventDefault();
						addField();
					}}
					disabled={mode === "view"}
				>
					<Plus className="mr-2 size-4" />
					Adicionar campo
				</Button>
			</div>
			<div className="hidden w-full overflow-auto rounded-lg border lg:block">
				<DndContext
					collisionDetection={closestCenter}
					modifiers={[restrictToVerticalAxis, restrictToParentElement]}
					onDragStart={e => setActiveId(String(e.active.id))}
					onDragEnd={e => {
						handleDragEnd(e);
						setActiveId(null);
					}}
					sensors={sensors}
					id={sortableId}
					measuring={{ droppable: { strategy: MeasuringStrategy.Always } }}
				>
					<div className="relative">
						<div ref={headerRef} className="sticky top-0 z-10 w-full overflow-x-auto">
							<Table className="w-full table-fixed">
								<TableHeader className="bg-primary">
									{table.getHeaderGroups().map(headerGroup => (
										<TableRow key={headerGroup.id}>
											{headerGroup.headers.map(header => (
												<TableHead
													key={header.id}
													colSpan={header.colSpan}
													className="text-center text-white"
													style={{ width: header.column.columnDef.meta?.width as string }}
												>
													{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
												</TableHead>
											))}
										</TableRow>
									))}
								</TableHeader>
							</Table>
						</div>
						<div ref={bodyRef} className="w-full overflow-x-auto">
							<Table className="w-full table-fixed">
								<TableBody>
									<SortableContext items={dataGroupsId} strategy={verticalListSortingStrategy}>
										{fieldsGroups.length > 0 ? (
											fieldsGroups.map(group => (
												<GroupItem
													isOverlay={false}
													key={group.tempId}
													group={group}
													colSpan={table.getHeaderGroups()[0]?.headers.length || 0}
													columns={inspectionFormColumns}
													autoFocus={group.tempId === lastGroupId}
													mode={mode}
												/>
											))
										) : (
											<TableRow>
												<TableCell
													colSpan={table.getHeaderGroups()[0]?.headers.length || 0}
													className="text-muted-foreground h-24 text-center"
												>
													{`Nenhum campo adicionado. Clique em "Adicionar campo" ou "Adicionar grupo" para começar.`}
												</TableCell>
											</TableRow>
										)}
									</SortableContext>
								</TableBody>
							</Table>
						</div>
					</div>
					<DragOverlay>
						{activeGroup && activeGroup.tempId.startsWith("empty") ? (
							<div className="w-full">
								<Table>
									<TableBody>
										<GroupItem
											isOverlay={true}
											group={activeGroup}
											columns={inspectionFormColumns}
											colSpan={table.getHeaderGroups()[0]?.headers.length || 0}
											mode={mode}
										/>
									</TableBody>
								</Table>
							</div>
						) : activeGroup ? (
							<GroupHeader group={activeGroup} isOverlay={true} mode={mode} />
						) : null}
					</DragOverlay>
				</DndContext>
			</div>
		</div>
	);
};
