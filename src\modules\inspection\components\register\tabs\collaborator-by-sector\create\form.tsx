import React from "react";
import { Input } from "@/shared/components/shadcn/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Button } from "@/shared/components/shadcn/button";
import { UseFormReturn } from "react-hook-form";
import { TCreateCollabSectorSchema } from "@/modules/inspection/validators/collaborator-by-sector/create";
import { GenericSearchSelect } from "@/shared/components/custom/generic-search-select";

import useFindAllMeasures from "@/modules/inspection/hooks/measures/list/find-all.hook";

interface IFormCreateMeasuresProps {
	onClose: () => void;
	methods: UseFormReturn<TCreateCollabSectorSchema>;
	onSubmit: (data: TCreateCollabSectorSchema) => void;
}

export default function FormCreateMeasures({ onClose, methods, onSubmit }: IFormCreateMeasuresProps) {
	return (
		<Form {...methods}>
			<form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
				<FormField
					control={methods.control}
					name="pin"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Pin</FormLabel>
							<FormControl>
								<Input {...field} placeholder="Digite o pin (Os 3 primeiros digitos do CPF)" />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={methods.control}
					name="collab"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Nome do colaborador</FormLabel>
							<FormControl>
								<GenericSearchSelect
									value={field.value}
									useDataHook={useFindAllMeasures}
									onChange={value => field.onChange(value)}
									placeholder="Selecione..."
									searchPlaceholder="Buscar colaborador..."
									loadingText="Carregando..."
									emptyText="Nenhuma colaborador encontrado."
									width="w-full"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={methods.control}
					name="sector"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Setor</FormLabel>
							<FormControl>
								<GenericSearchSelect
									value={field.value}
									useDataHook={useFindAllMeasures}
									onChange={value => field.onChange(value)}
									placeholder="Selecione..."
									searchPlaceholder="Buscar setor..."
									loadingText="Carregando..."
									emptyText="Nenhuma setor encontrado."
									width="w-full"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="flex justify-end gap-2 pt-4">
					<Button type="button" variant="outline" onClick={onClose}>
						Cancelar
					</Button>
					<Button type="submit">Salvar</Button>
				</div>
			</form>
		</Form>
	);
}
