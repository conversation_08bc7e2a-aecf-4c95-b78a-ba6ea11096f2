import { Separator } from "@/shared/components/shadcn/separator";
import { SidebarTrigger } from "@/shared/components/shadcn/sidebar";
import { DynamicBreadcrumb } from "../breadcrumb/dynamic-breadcrumb";

export const Header = () => {
	return (
		<header className="flex items-center justify-between h-[60px]   border-b border-border">
			<div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
				<div className="flex items-center gap-2 md:hidden">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mx-2 data-[orientation=vertical]:h-4" />
				</div>
				<DynamicBreadcrumb />
			</div>
		</header>
	);
};
