import axios from "axios";
import { cookieHeadersInterceptor } from "./interceptors/cookie-headers.interceptor";
import { cookieResponseInterceptor } from "./interceptors/cookie-response.interceptor";
import { setupRefreshInterceptor } from "./interceptors/refresh.intercetpor";

export const API_ROUTE = process.env.API_ROUTE || "/api/backend";

export const axiosInstance = axios.create({
	baseURL: API_ROUTE,
	withCredentials: true,
});

cookieHeadersInterceptor(axiosInstance);
cookieResponseInterceptor(axiosInstance);
setupRefreshInterceptor(axiosInstance);
