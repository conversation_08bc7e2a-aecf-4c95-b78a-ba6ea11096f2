"use client";
import { IChatStreamRequest, IStreamingOptions } from "../../types/streaming.type";
import { StreamErrorHandler } from "./error-handler.service";
import { StreamRequestHandler } from "./request-handler.service";
import { StreamProcessor } from "./streaming-processor";

export class ChatStreamService {
	private requestHandler: StreamRequestHandler;
	private abortController: AbortController | null = null;

	constructor(requestHandler: StreamRequestHandler) {
		this.requestHandler = requestHandler;
	}

	async streamChat(request: IChatStreamRequest, options: IStreamingOptions = {}): Promise<void> {
		this.abortStream();
		this.abortController = new AbortController();
		const signal = options.signal || this.abortController.signal;
		await this.requestHandler.handle(request, options, signal);
	}

	abortStream(): void {
		this.abortController?.abort();
		this.abortController = null;
	}

	isStreaming(): boolean {
		return !!this.abortController;
	}

	cleanup(): void {
		this.abortStream();
	}
}

const processor = new StreamProcessor();
const errorHandler = new StreamErrorHandler();
export const chatStreamService = new ChatStreamService(new StreamRequestHandler(processor, errorHandler));
