"use client";

import { pathService } from "@/config/path-manager/service";
import { useNavigatePaths } from "@/shared/hooks/utils";
import { useActiveMenuItem } from "./active-menu-item.hook";

type BreadcrumbPathItem = Readonly<{
	label: string;
	href?: string;
	description?: string;
	isActive?: boolean;
}>;

const extractDynamicSegments = (basePath: string, currentPath: string): string[] => {
	if (!basePath || !currentPath.startsWith(basePath) || basePath === currentPath) {
		return [];
	}

	const remainingPath = currentPath.substring(basePath.length);
	return remainingPath
		.split("/")
		.filter(Boolean)
		.map(segment => decodeURIComponent(segment));
};

const addUniqueBreadcrumbItem = (breadcrumbs: ReadonlyArray<BreadcrumbPathItem>, item: BreadcrumbPathItem): BreadcrumbPathItem[] => {
	const exists = breadcrumbs.some(existing => existing.label === item.label && existing.href === item.href);
	return exists ? [...breadcrumbs] : [...breadcrumbs, item];
};

export const useDynamicBreadcrumb = (): { uniqueBreadcrumbPath: Readonly<BreadcrumbPathItem[]> } => {
	const { activeGroup, currentItem, parentOfActiveSubItemId } = useActiveMenuItem();
	const { pathname } = useNavigatePaths();

	let breadcrumbs: BreadcrumbPathItem[] = [
		{
			label: "Tela Inicial",
			href: "/",
			isActive: pathname === "/",
		},
	];

	if (pathname === "/") return { uniqueBreadcrumbPath: breadcrumbs };

	if (activeGroup) {
		breadcrumbs = addUniqueBreadcrumbItem(breadcrumbs, {
			label: activeGroup.title,
			isActive: false,
		});
	}

	if (parentOfActiveSubItemId) {
		const parentItem = pathService.getItemById(parentOfActiveSubItemId);
		if (parentItem && parentItem.route.path !== "/") {
			breadcrumbs = addUniqueBreadcrumbItem(breadcrumbs, {
				label: parentItem.name,
				href: parentItem.route.path,
				description: parentItem.description,
				isActive: false,
			});
		}
	}

	if (currentItem && currentItem.route.path !== "/") {
		breadcrumbs = addUniqueBreadcrumbItem(breadcrumbs, {
			label: currentItem.name,
			href: currentItem.route.path,
			description: currentItem.description,
			isActive: pathname === currentItem.route.path,
		});
	}

	if (currentItem) {
		const dynamicSegments = extractDynamicSegments(currentItem.route.path, pathname);
		dynamicSegments.forEach((segment, index) => {
			const isLast = index === dynamicSegments.length - 1;
			breadcrumbs = addUniqueBreadcrumbItem(breadcrumbs, {
				label: segment,
				href: isLast ? undefined : pathname,
				isActive: isLast,
			});
		});
	}

	return { uniqueBreadcrumbPath: breadcrumbs };
};
